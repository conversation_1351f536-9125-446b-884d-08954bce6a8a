import { useState, useEffect, useCallback } from 'react';

import { supabase } from 'src/lib/supabase';

export const useAuth = (table: string) => {
  const [auth, setAuth] = useState<any>([]);
  const [loading, setIsloading] = useState(true);
  const [error, setError] = useState<any>();

  const fetchStationsData = useCallback(async () => {
    try {
      const { data, error: bug } = await supabase.from(table).select('*');
      if (bug) {
        console.error('Supabase error:', bug);
        setError(bug.message);
      }

      console.log(data, ' user data auth');
      return setAuth(data || []);
    } catch (err) {
      console.error('Error fetching stations data:', err);
      return err;
    } finally {
      setIsloading(false);
    }
  }, [table]);

  useEffect(() => {
    fetchStationsData();
  }, [fetchStationsData]);

  return { data: auth, error, loading };
};
