import type { PostgrestError } from '@supabase/supabase-js';
import type { IBook, IBooksData, IBooksTableData } from 'src/types/books';

import { useState, useEffect, useCallback } from 'react';

import { supabase } from 'src/lib/supabase';

const TABLE = 'books';

interface UseBooksListParams {
  stationId?: string | null;
  status?: string | null;
}

export const useBooksList = (params: UseBooksListParams = {}) => {
  const [booksData, setBooksData] = useState<IBook[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const { stationId, status } = params;

  const fetchBooksData = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Start building the query
      let query = supabase.from(TABLE).select(`
        id,
        name,
        author,
        category:categories (
          id,
          name,
          color
        ),
        borrowerId: borrower_id,
        isActive: is_active,
        status
      `);

      if (stationId) {
        query = query.eq('station_id', stationId);
      }

      if (status) {
        query = query.eq('status', status);
      }

      // Execute the query
      const { data, error: booksError } = (await query.order('created_at', {
        ascending: false,
      })) as unknown as { data: IBooksData[]; error: PostgrestError };

      if (booksError) {
        console.error('Supabase error:', booksError);
        setError(booksError.message);
        return;
      }

      const transformedData: IBook[] = data.map((book) => {
        const isAvailable = book.borrowerId === null;
        const dataTrans: IBook & Partial<Pick<IBooksData, 'borrowerId'>> = {
          ...book,
          isAvailable,
        };
        delete dataTrans.borrowerId;
        return dataTrans;
      });

      setBooksData(transformedData || []);
    } catch (err: any) {
      console.error('Error fetching books data:', err);
      setError(err.message || 'An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  }, [stationId, status]);

  // Refetch when any parameter changes
  useEffect(() => {
    fetchBooksData();
  }, [fetchBooksData]);

  // Manual refetch function
  const refetch = useCallback(() => {
    fetchBooksData();
  }, [fetchBooksData]);

  return {
    data: booksData,
    isLoading,
    error,
    setData: setBooksData,
    refetch,
  };
};

export const useBooksData = (table: string) => {
  const [booksData, setBooksData] = useState<IBooksTableData[]>([]);
  const [loading, setIsloading] = useState(true);
  const [booksError, setError] = useState<string>('');

  interface BookToTake {
    name: string;
    author: string;
    description: string;
    category_id: string;
  }

  const fetchBooksData = useCallback(async () => {
    try {
      const { data, error } = await supabase.from('books').select(`
  id,
  name,
  author,
  category:categories (
    id,
    name,
    description,
    color
  ),
  station_id,
  borrower:books_borrower_id_fkey (
    id,
    email,
    user_name
  ),
  donator:books_donator_id_fkey (
    id,
    email,
    user_name
  ),
  description,
  is_active,
  status,
  borrower_method,
  created_at,
  updated_at
`);

      if (error) {
        console.error('Supabase error:', error);
        setError(error.message);
      }
      return setBooksData(data || ([] as any));
    } catch (error) {
      console.error('Error fetching stations data:', error);
      return error;
    } finally {
      setIsloading(false);
    }
  }, []);

  const createBook = async (bookData: Omit<IBooksTableData, 'id'>) => {
    try {
      const { error } = await supabase.from(table).insert([bookData]);
      if (error) {
        console.error('Supabase error:', error);
        setError(error.message);
      }
      fetchBooksData();
    } catch (error) {
      console.error('Error creating book:', error);
      setError(error.message);
    }
  };

  const userCreateBook = async (userBookData: Omit<BookToTake, 'id'>) => {
    try {
      const dataToInsert = {
        ...userBookData,
        status: 'pending',
        is_active: true,
      };
      console.log(dataToInsert, '001');
      const { error } = await supabase.from(table).insert([dataToInsert]);
      if (error) {
        console.error('Supabase error:', error);
        setError(error.message);
      }
      fetchBooksData();
    } catch (error) {
      console.error('Error creating book:', error);
      setError(error.message);
    }
  };

  const updateBook = async (id: string, updates: Partial<IBooksTableData>) => {
    try {
      const { error } = await supabase.from(table).update(updates).eq('id', id);
      if (error) {
        console.error('Supabase error:', error);
        setError(error.message);
      }
      fetchBooksData();
    } catch (error) {
      console.error('Error updating book:', error);
      setError(error.message);
    }
  };

  const deleteBook = async (id: string) => {
    try {
      const { error } = await supabase.from(table).delete().eq('id', id);
      if (error) {
        console.error('Supabase error:', error);
        setError(error.message);
      }
      fetchBooksData();
    } catch (error) {
      console.error('Error deleting book:', error);
      setError(error.message);
    }
  };

  const acceptBookRequest = async (id: string) => {
    try {
      const { error } = await supabase.from(table).update({ status: 'approved' }).eq('id', id);

      if (error) {
        console.error('Supabase error:', error);
        setError(error.message);
      }

      fetchBooksData();
    } catch (error) {
      console.error('Error accept request:', error);
      setError(error.message);
    }
  };
  const rejectBookRequest = async (id: string) => {
    try {
      const { error } = await supabase.from(table).update({ status: 'rejected' }).eq('id', id);

      if (error) {
        console.error('Supabase error:', error);
        setError(error.message);
      }

      fetchBooksData();
    } catch (error) {
      console.error('Error accept request:', error);
      setError(error.message);
    }
  };

  const getBooksByShelfId = async (shelfId: string) => {
    try {
      const { data, error } = await supabase
        .from('books')
        .select(
          `
        id,
  name,
  author,
  category:categories (
    id,
    name,
    description,
    color
  )
        `
        )
        .eq('station_id', shelfId);

      if (error) {
        console.error('Supabase error:', error);
        setError(error.message);
      }

      return data;
    } catch (error) {
      console.error('Error fetching books by stationon ID:', error);
      setError(error.message);
      return [];
    }
  };

  const getBooksByBorrowerId = async (userId: string) => {
    try {
      const { data, error } = await supabase.from('books').select('*').eq('borrower_id', userId);

      if (error) {
        console.error('Supabase error:', error);
        setError(error.message);
      }

      return data;
    } catch (error) {
      console.error('Error fetching books by borrower ID:', error);
      setError(error.message);
      return [];
    }
  };

  useEffect(() => {
    fetchBooksData();
  }, [fetchBooksData]);

  return {
    booksData,
    loading,
    setBooksData,
    booksError,
    createBook,
    fetchBooksData,
    updateBook,
    deleteBook,
    acceptBookRequest,
    rejectBookRequest,
    userCreateBook,
    getBooksByShelfId,
    getBooksByBorrowerId,
  };
};
