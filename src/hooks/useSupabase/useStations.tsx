import type { PostgrestError } from '@supabase/supabase-js';
import type { IStationEditCreateSchema } from 'src/schema/station';
import type {
  IStation,
  IStationData,
  IStationsItem,
  IStationWithDate,
  IStationWithBookCount,
} from 'src/types/stations';

import { useState, useEffect, useCallback } from 'react';

import { toSnakeCase } from 'src/utils/misc';

import { supabase } from 'src/lib/supabase';

const TABLE = 'stations';

export const useStationList = () => {
  const [data, setData] = useState<IStation[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetch = async () => {
    try {
      const { data: stationData, error: stationError } = (await supabase
        .from(TABLE)
        .select(
          `id, name, location, isActive: is_active, createdAt: created_at, updatedAt: updated_at, books(count)`
        )
        .order('created_at', { ascending: true })) as unknown as {
        data: IStationWithBookCount[];
        error: PostgrestError;
      };

      if (stationError) setError(stationError?.message ?? 'error while fetching stations');

      if (stationData) {
        const result = stationData.map(({ books, ...rest }) => ({
          ...rest,
          bookCount: books[0].count,
        }));
        setData(result);
      }
    } catch (err) {
      setError(err?.message ?? 'error while fetching stations');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetch();
  }, []);

  return { data, isLoading, error, setData, refetch: fetch };
};

export const useStationDetails = (id: string) => {
  const [data, setData] = useState<IStationData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // eslint-disable-next-line consistent-return
  const fetch = useCallback(async () => {
    try {
      const { data: stationData, error: stationError } = (await supabase
        .from(TABLE)
        .select(`id, name, location, isActive: is_active`)
        .eq('id', id)
        .single()) as unknown as {
        data: IStationData;
        error: PostgrestError;
      };

      if (stationError) {
        setError(stationError?.message ?? 'error while fetching station');
      }

      if (!stationData) setError('Station not found');

      if (stationData) {
        setData(stationData);
      }
    } catch (err) {
      setError(err?.message ?? 'error while fetching stations');
    } finally {
      setIsLoading(false);
    }
  }, [id]);

  useEffect(() => {
    fetch();
  }, [fetch]);

  return { data, isLoading, error, setData, refetch: fetch };
};

export const deleteStation = async (stationId: string) => {
  try {
    const { error: deleteError } = await supabase.from(TABLE).delete().eq('id', stationId);
    if (deleteError) throw deleteError;
  } catch (err: any) {
    console.error('Error deleting station:', err);
    throw err;
  }
};

export const createStation = async (stationData: IStationEditCreateSchema) => {
  const updateValues = toSnakeCase(stationData);

  try {
    const {
      data: [createdStation],
      error: createError,
    } = (await supabase
      .from(TABLE)
      .insert(updateValues)
      .select(
        `id, name, location, isActive: is_active, createdAt: created_at, updatedAt: updated_at`
      )) as unknown as {
      data: [IStationWithDate];
      error: PostgrestError;
    };
    if (createError) throw createError;

    const data: IStation = {
      ...createdStation,
      bookCount: 0,
    };

    return data;
  } catch (err: any) {
    console.error('Error creating station:', err);
    throw err;
  }
};

export const updateStation = async (id: string, updates: Partial<IStationWithDate>) => {
  const updateValues = toSnakeCase(updates);
  try {
    const { error: updateError } = await supabase.from(TABLE).update(updateValues).eq('id', id);
    if (updateError) throw updateError;
  } catch (err: any) {
    console.error('Error updating station:', err);
    throw err;
  }
};

export const useStationsData = (table: string) => {
  const [stationsData, setData] = useState<IStationsItem[]>([]);
  const [loading, setIsloading] = useState(true);
  const [stationsError, setError] = useState<string>('');

  const fetchStationsData = useCallback(async () => {
    try {
      const { data, error } = await supabase.from(table).select('*');
      if (error) {
        console.error('Supabase error:', error);
        setError(error.message);
      }
      return setData(data || []);
    } catch (error) {
      console.error('Error fetching stations data:', error);
      return error;
    } finally {
      setIsloading(false);
    }
  }, [table]);

  const createShelf = async (shelfData: Omit<IStationsItem, 'id'>) => {
    try {
      const { error } = await supabase.from(table).insert([shelfData]);
      if (error) {
        console.error('Supabase error:', error);
        setError(error.message);
      }
      fetchStationsData();
    } catch (error) {
      console.error('Error creating station:', error);
      setError(error.message);
    }
  };

  const deleteShelf = async (id: string) => {
    try {
      const { error } = await supabase.from(table).delete().eq('id', id);
      if (error) {
        console.error('Supabase error:', error);
        setError(error.message);
      }
      fetchStationsData();
    } catch (error) {
      console.error('Error deleting station:', error);
      setError(error.message);
    }
  };

  const updateShelf = async (id: string, updates: Partial<IStationsItem>) => {
    try {
      const { error } = await supabase.from(table).update(updates).eq('id', id);
      if (error) {
        console.error('Supabase error:', error);
        setError(error.message);
      }
      fetchStationsData();
    } catch (error) {
      console.error('Error updating station:', error);
      setError(error.message);
    }
  };

  useEffect(() => {
    fetchStationsData();
  }, [fetchStationsData]);

  return {
    data: stationsData,
    error: stationsError,
    loading,
    createShelf,
    updateShelf,
    deleteShelf,
  };
};
