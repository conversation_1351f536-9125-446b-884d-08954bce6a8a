import { useState } from 'react';

import Alert from '@mui/material/Alert';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import LoadingButton from '@mui/lab/LoadingButton';

import { paths } from 'src/routes/paths';
import { useSearchParams } from 'src/routes/hooks';

import { buildUrlWithUpdatedParams } from 'src/utils/url';

import { CONFIG } from 'src/config-global';
import { AUTH_RETURN_TO_PARAM } from 'src/constants/auth';

import { SocialIcon } from 'src/components/iconify';

import { signInWithGoogle } from 'src/auth/context/supabase';

// ----------------------------------------------------------------------

export function SupabaseSignInView() {
  const [errorMsg, setErrorMsg] = useState('');
  const [isGoogleLoading, setIsGoogleLoading] = useState(false);

  const searchParams = useSearchParams();

  // Extract the returnTo parameter from URL query string
  // This contains the original page URL that user was trying to access before being redirected to sign-in
  const returnTo = searchParams.get(AUTH_RETURN_TO_PARAM);

  /**
   * Construct the post-authentication redirect URL
   *
   * Flow explanation:
   * 1. User tries to access protected route (e.g., /dashboard/profile)
   * 2. AuthGuard redirects to /sign-in?returnTo=/dashboard/profile
   * 3. After OAuth success, Supabase redirects to our callback URL with returnTo preserved
   * 4. Callback handler reads returnTo and redirects user to their original destination
   *
   * If returnTo exists: Preserve it in the OAuth callback URL so user returns to intended page
   * If no returnTo: Use default redirect path (usually dashboard/home)
   */
  const postSignUrl = returnTo
    ? `${CONFIG.site.basePath}${paths.auth.signIn}?${buildUrlWithUpdatedParams(searchParams, AUTH_RETURN_TO_PARAM, returnTo)}`
    : CONFIG.auth.redirectPath;

  /**
   * Handle Google OAuth sign-in with deep linking support
   *
   * The redirectTo parameter tells Supabase where to send the user after successful authentication
   * This preserves the user's original intended destination throughout the OAuth flow
   */
  const handleGoogleSignIn = async () => {
    try {
      setIsGoogleLoading(true);
      setErrorMsg('');

      // Initiate OAuth with the constructed redirect URL that preserves returnTo parameter
      await signInWithGoogle({ redirectTo: postSignUrl });
    } catch (error) {
      setErrorMsg(error instanceof Error ? error.message : 'Google sign-in failed');
      setIsGoogleLoading(false);
    }
  };

  return (
    <>
      <Stack spacing={1.5} sx={{ mb: 5 }}>
        <Typography variant="h5">Sign in to your account</Typography>
      </Stack>

      {!!errorMsg && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {errorMsg}
        </Alert>
      )}
      <LoadingButton
        fullWidth
        color="inherit"
        size="large"
        type="button"
        variant="contained"
        onClick={handleGoogleSignIn}
        loading={isGoogleLoading}
        sx={{ mb: 2 }}
        startIcon={!isGoogleLoading && <SocialIcon icon="google" width={22} />}
      >
        Sign in with Google
      </LoadingButton>
    </>
  );
}
