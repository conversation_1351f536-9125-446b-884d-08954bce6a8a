import type { IBooksTableData } from 'src/types/books';
import type { IUseBooleanReturn } from 'src/hooks/use-boolean';

import { z as zod } from 'zod';
import { useEffect } from 'react';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';

import Dialog from '@mui/material/Dialog';
import Button from '@mui/material/Button';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import LoadingButton from '@mui/lab/LoadingButton';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import { Grid, TextField, Autocomplete } from '@mui/material';

import { useBooksData } from 'src/hooks/useSupabase/useBooks';
import { useStationsData } from 'src/hooks/useSupabase/useStations';
import { useCategoryData } from 'src/hooks/useSupabase/useCategories';

import { toast } from 'src/components/snackbar';
import { Iconify } from 'src/components/iconify';
import { Form, RHFTextField, RHFAutocomplete } from 'src/components/hook-form';

const defaultValues = {
  name: '',
  author: '',
  categoryId: '',
  shelfId: '',
  description: '',
};

function BookUpsertDialog({
  dialogControl,
  book,
  onClose,
  onCreateBook,
  onUpdateBook,
}: {
  dialogControl: IUseBooleanReturn;
  book: IBooksTableData | null;
  onClose: () => void;
  onCreateBook: (bookData: Omit<IBooksTableData, 'id'>) => Promise<void>;
  onUpdateBook: (id: string, updates: Partial<IBooksTableData>) => Promise<void>;
}) {
  const NewBookSchema = zod.object({
    name: zod.string().min(1, 'Book name is required'),
    author: zod.string().min(1, 'Author is required'),
    categoryId: zod.string().min(1, 'Category is required'),
    shelfId: zod.string().min(1, 'Shelf is required'),
    description: zod.string().min(1, 'Description is required'),
  });

  const methods = useForm({
    resolver: zodResolver(NewBookSchema),
    defaultValues,
  });

  const {
    handleSubmit,
    control,
    formState: { isSubmitting },
    reset,
  } = methods;

  const isUpdating = !!book;

  const { data: categoriesData } = useCategoryData('categories');
  const { data: stationsData } = useStationsData('stations');
  const { booksData } = useBooksData('books');

  const activeCategories = categoriesData?.filter((item: any) => item.is_status === true) ?? [];

  useEffect(() => {
    if (book) {
      const formData = {
        name: book.name,
        author: book.author,
        categoryId: book?.category?.id || '',
        shelfId: book.station_id || '',
        description: book.description,
      };
      reset(formData);
    } else {
      reset(defaultValues);
    }
  }, [book, reset]);

  const onSubmit = handleSubmit(async (data) => {
    try {
      const selectedCategory = categoriesData?.find((cat: any) => cat.id === data.categoryId);
      const selectedShelf = stationsData.find((station: any) => station.id === data.shelfId);

      const bookData: any = {
        name: data.name,
        author: data.author,
        category_id: selectedCategory?.id,
        station_id: selectedShelf?.id,
        description: data.description,
        status: 'approved',
      };
      console.log('🚀 ~ onSubmit ~ bookData:', bookData);

      if (isUpdating && book?.id) {
        // Update existing book
        await onUpdateBook(book.id, bookData);
        toast.success('Book updated successfully!');
        console.log('Book updated successfully');
      } else {
        // Create new book
        await onCreateBook(bookData);
        toast.success('Book created successfully!');
        console.log('Book created successfully');
      }

      closeDialog();
    } catch (error) {
      console.error('Error saving book:', error);
      toast.error('Failed to save book. Please try again.');
    }
  });

  const closeDialog = () => {
    reset(defaultValues);
    onClose();
  };

  return (
    <Dialog
      open={dialogControl.value}
      onClose={closeDialog}
      fullWidth
      maxWidth="md"
      sx={{
        '& .MuiPaper-elevation': {
          maxWidth: '800px',
        },
      }}
    >
      <Form methods={methods} onSubmit={onSubmit}>
        <DialogTitle>
          <Typography variant="h6" component="span" fontWeight="bold">
            {isUpdating ? `Edit: ${book?.name}` : 'New Book'}
          </Typography>
        </DialogTitle>

        <IconButton
          aria-label="close"
          onClick={closeDialog}
          sx={{
            position: 'absolute',
            right: 8,
            top: 20,
            color: (theme) => theme.palette.grey[500],
          }}
        >
          <Iconify icon="mingcute:close-line" />
        </IconButton>

        <DialogContent
          sx={{
            display: 'flex',
            flexDirection: 'column',
            gap: 3,
            pt: 2,
          }}
        >
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <RHFTextField
                name="name"
                label="Book Name"
                size="small"
                fullWidth
                placeholder="Enter book title"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <RHFAutocomplete
                name="author"
                label="Author"
                size="small"
                placeholder="Select or enter author name"
                freeSolo
                options={booksData ? Array.from(new Set(booksData.map((b: any) => b.author))) : []}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <Controller
                name="categoryId"
                control={control}
                render={({ field, fieldState: { error } }) => (
                  <Autocomplete
                    {...field}
                    options={activeCategories}
                    getOptionLabel={(option) => option?.name || ''}
                    value={activeCategories.find((cat) => cat.id === field.value) || null}
                    onChange={(event, newValue) => {
                      field.onChange(newValue?.id || '');
                    }}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Category"
                        size="small"
                        fullWidth
                        placeholder="Select category"
                        error={!!error}
                        helperText={error?.message}
                      />
                    )}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <Controller
                name="shelfId"
                control={control}
                render={({ field, fieldState: { error } }) => (
                  <Autocomplete
                    {...field}
                    options={stationsData}
                    getOptionLabel={(option) => `${option.name} (${option.location})`}
                    value={stationsData.find((station) => station.id === field.value) || null}
                    onChange={(event, newValue) => {
                      field.onChange(newValue?.id || '');
                    }}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Shelf"
                        size="small"
                        fullWidth
                        placeholder="Select station"
                        error={!!error}
                        helperText={error?.message}
                      />
                    )}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12}>
              <RHFTextField
                name="description"
                label="Description"
                size="small"
                rows={4}
                fullWidth
                multiline
                placeholder="Enter book description"
              />
            </Grid>
          </Grid>
        </DialogContent>

        <DialogActions sx={{ p: 3 }}>
          <Button onClick={closeDialog} variant="outlined" color="inherit">
            Cancel
          </Button>
          <LoadingButton color="primary" type="submit" variant="contained" loading={isSubmitting}>
            {isUpdating ? 'Update Book' : 'Create Book'}
          </LoadingButton>
        </DialogActions>
      </Form>
    </Dialog>
  );
}

export default BookUpsertDialog;
