import type { IBooksTableData } from 'src/types/books';

import { useState } from 'react';

import Button from '@mui/material/Button';
import Tooltip from '@mui/material/Tooltip';
import TableRow from '@mui/material/TableRow';
import TableCell from '@mui/material/TableCell';
import IconButton from '@mui/material/IconButton';
import { Box, Select, MenuItem, Typography } from '@mui/material';

import { useBoolean } from 'src/hooks/use-boolean';

import { fDateTime } from 'src/utils/format-time';
import { lightenHexColor } from 'src/utils/lighten-hex-color';

import { Label } from 'src/components/label';
import { toast } from 'src/components/snackbar';
import { Iconify } from 'src/components/iconify';
import { ConfirmDialog } from 'src/components/custom-dialog';

// ----------------------------------------------------------------------

type Props = {
  row: IBooksTableData;
  onAccept: (id: string) => Promise<void>;
  onReject: (id: string) => Promise<void>;
  onView: () => void;
  onDelete: (id: string) => Promise<void>;
};

export default function BookRequestsTableRow({ row, onAccept, onReject, onDelete }: Props) {
  const { id, name, author, category, created_at, donator, status } = row;

  const [currentStatus, setCurrentStatus] = useState(status);

  const confirmDelete = useBoolean();

  const handleStatusUpdate = async (newStatus: typeof status) => {
    setCurrentStatus(newStatus);
    try {
      if (newStatus === 'approved') {
        await onAccept(id);
      } else if (newStatus === 'rejected') {
        await onReject(id);
      }
      console.log('Request status updated successfully');
    } catch (error) {
      console.error('Error updating request status:', error);
      toast.error('Failed to update request status. Please try again.');
    }
  };

  const handleDelete = async () => {
    confirmDelete.onFalse();
    try {
      await onDelete(id);
      console.log('Request deleted successfully');
    } catch (error) {
      console.error('Error deleting request:', error);
      toast.error('Failed to delete request. Please try again.');
    }
  };

  return (
    <>
      <TableRow hover>
        <TableCell
          sx={{
            maxWidth: '310px',
          }}
        >
          <Typography
            variant="body2"
            sx={{
              fontWeight: '900',
              lineHeight: '22px',
              whiteSpace: 'nowrap',
              textOverflow: 'ellipsis',
              overflow: 'hidden',
            }}
          >
            {name}
          </Typography>

          <Typography
            variant="body2"
            sx={{ fontWeight: '400', lineHeight: '22px', color: 'grey.500' }}
          >
            {author}
          </Typography>
        </TableCell>

        <TableCell sx={{ maxWidth: '150px' }}>
          {category && (
            <Label
              variant="soft"
              sx={{
                backgroundColor: lightenHexColor(category.color, 88),
                color: category.color,
                fontWeight: '700',
                fontSize: '12px',
                lineHeight: '20px',
              }}
            >
              {category.name}
            </Label>
          )}
        </TableCell>

        <TableCell sx={{ maxWidth: '180px' }}>
          <Typography
            variant="body2"
            sx={{
              fontWeight: '500',
              lineHeight: '22px',
            }}
          >
            {donator?.user_name}
          </Typography>
          <Typography
            variant="caption"
            sx={{
              color: 'text.secondary',
              lineHeight: '18px',
            }}
          >
            {donator?.email}
          </Typography>
        </TableCell>

        <TableCell sx={{ maxWidth: '150px' }}>
          <Typography
            variant="body2"
            sx={{
              fontWeight: '400',
              lineHeight: '22px',
            }}
          >
            {fDateTime(created_at)}
          </Typography>
        </TableCell>

        <TableCell align="center" sx={{ px: 1, whiteSpace: 'nowrap' }}>
          <Select
            autoFocus
            value={currentStatus}
            onChange={(e) => {
              handleStatusUpdate(e.target.value as typeof status);
            }}
            size="small"
            inputProps={{ id: 'max-width-label' }}
          >
            <MenuItem value="approved">approved</MenuItem>
            <MenuItem value="rejected">rejected</MenuItem>
            <MenuItem value="pending">pending</MenuItem>
          </Select>
        </TableCell>

        <TableCell align="right" sx={{ px: 1, whiteSpace: 'nowrap' }}>
          <Tooltip title="View Details" placement="top" arrow>
            <IconButton onClick={onView}>
              <Iconify icon="solar:eye-bold" />
            </IconButton>
          </Tooltip>
        </TableCell>

        <TableCell align="right" sx={{ px: 1, whiteSpace: 'nowrap' }}>
          <Tooltip title="Delete" placement="top" arrow>
            <IconButton onClick={confirmDelete.onTrue} sx={{ color: 'error.dark' }}>
              <Iconify icon="solar:trash-bin-trash-bold" />
            </IconButton>
          </Tooltip>
        </TableCell>
      </TableRow>

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        open={confirmDelete.value}
        onClose={confirmDelete.onFalse}
        title={
          <Box display="flex" gap={2} alignItems="center">
            <Iconify icon="solar:trash-bin-trash-bold" /> Delete Request
          </Box>
        }
        content={
          <>
            <Typography variant="subtitle1">Are you sure?</Typography>
            <Typography variant="body1">This request will be permanently removed.</Typography>
          </>
        }
        action={
          <Button variant="contained" color="error" onClick={handleDelete}>
            Delete
          </Button>
        }
      />
    </>
  );
}
