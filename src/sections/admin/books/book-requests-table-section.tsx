import type { IBooksTableData, IBookRequestTableFilters } from 'src/types/books';

import { useState } from 'react';
import { isEqual } from 'lodash';

import Card from '@mui/material/Card';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableContainer from '@mui/material/TableContainer';

import { useDebounce } from 'src/hooks/use-debounce';
import { useCategoryData } from 'src/hooks/useSupabase/useCategories';

import { TABLE_DEFAULTS } from 'src/constants/table';
import {
  BOOK_REQUESTS_TABLE_COLUMNS,
  DEFAULT_BOOK_REQUESTS_TABLE_FILTERS,
} from 'src/constants/books';

import { toast } from 'src/components/snackbar';
import { Scrollbar } from 'src/components/scrollbar';
import { EmptyContent } from 'src/components/empty-content';
import {
  useTable,
  emptyRows,
  TableNoData,
  TableEmptyRows,
  TableHeadCustom,
  TablePaginationCustom,
} from 'src/components/table';

import BookRequestsTableRow from './book-requests-table-row';
import BookRequestsTableToolbar from './book-requests-table-toolbar';
import BookRequestsTableFiltersResult from './book-requests-table-filters-result';

type Props = {
  booksData: IBooksTableData[];
  loading: boolean;
  onAcceptRequest: (id: string) => Promise<void>;
  onRejectRequest: (id: string) => Promise<void>;
  onDeleteRequest: (id: string) => Promise<void>;
};

export default function BookRequestsTableSection({
  booksData,
  loading,
  onAcceptRequest,
  onRejectRequest,
  onDeleteRequest,
}: Props) {
  // -- Table state and config --
  const tableConfig = useTable();
  const [filters, setFilters] = useState(DEFAULT_BOOK_REQUESTS_TABLE_FILTERS);
  const { data: categoryData } = useCategoryData('categories');

  // -- Derived values --
  const denseHeight = tableConfig.dense
    ? TABLE_DEFAULTS.DENSE_HEIGHT
    : TABLE_DEFAULTS.DENSE_HEIGHT_WITH_MARGIN;

  const debouncedSearchQuery = useDebounce(filters.searchKeyword, 1500);

  // TODO: Replace with actual API call
  const data = booksData;

  // -- Apply filters to the data --
  const filteredData = data.filter((item) => {
    // Search filter - search in name, author, and requester name (case-insensitive, trimmed)
    const searchTerm = debouncedSearchQuery.trim().toLowerCase();
    const matchesSearch = searchTerm
      ? item.name.toLowerCase().includes(searchTerm) ||
        item.author.toLowerCase().includes(searchTerm)
      : // item.requestedBy.name.toLowerCase().includes(searchTerm) ||
        // item.requestedBy.email.toLowerCase().includes(searchTerm)
        true;

    // Category filter
    const matchesCategory = filters.categoryId ? item.category?.id === filters.categoryId : true;

    // Status filter
    const matchesStatus = filters.status ? item.status === filters.status : true;

    return matchesSearch && matchesCategory && matchesStatus;
  });

  // -- Derived table data --
  const tableData = filteredData;
  const totalCount = filteredData.length;
  const isFetching = loading;

  // -- Computed helpers --
  const canReset = !isEqual(DEFAULT_BOOK_REQUESTS_TABLE_FILTERS, filters);
  const emptyData = !tableData.length;
  const hasData = data.length > 0;
  const isFiltered = debouncedSearchQuery.length > 0 || filters.categoryId || filters.status;
  const shouldShowTable = isFetching || hasData;

  // -- Handlers --
  const handleFilters = <K extends keyof IBookRequestTableFilters>(
    name: K,
    value: IBookRequestTableFilters[K]
  ) => {
    tableConfig.onResetPage();
    setFilters((prevState) => ({
      ...prevState,
      [name]: value,
    }));
  };

  const handleResetFilters = () => {
    setFilters(DEFAULT_BOOK_REQUESTS_TABLE_FILTERS);
  };

  const handleAcceptRequest = async (id: string) => {
    try {
      await onAcceptRequest(id);
      toast.success('Request accepted successfully!');
    } catch (error) {
      console.error('Error accepting request:', error);
      toast.error('Failed to accept request. Please try again.');
    }
  };

  const handleRejectRequest = async (id: string) => {
    try {
      await onRejectRequest(id);
      toast.success('Request rejected successfully!');
    } catch (error) {
      console.error('Error rejecting request:', error);
      toast.error('Failed to reject request. Please try again.');
    }
  };

  const handleDeleteRequest = async (id: string) => {
    try {
      await onDeleteRequest(id);
      toast.success('Request deleted successfully!');
    } catch (error) {
      console.error('Error deleting request:', error);
      toast.error('Failed to delete request. Please try again.');
    }
  };

  return (
    <>
      {shouldShowTable ? (
        <Card>
          <BookRequestsTableToolbar
            filters={filters}
            onFilters={handleFilters}
            categoryOptions={categoryData.map((c) => ({ id: c.id, name: c.name }))}
          />

          {canReset && (
            <BookRequestsTableFiltersResult
              filters={filters}
              onFilters={handleFilters}
              onResetFilters={handleResetFilters}
              results={totalCount}
              categories={categoryData}
              sx={{ p: 2.5, pt: 0 }}
            />
          )}

          <TableContainer sx={{ position: 'relative', overflow: 'unset' }}>
            <Scrollbar>
              <Table size={tableConfig.dense ? 'small' : 'medium'} sx={{ minWidth: 960 }}>
                <TableHeadCustom headLabel={BOOK_REQUESTS_TABLE_COLUMNS} />

                <TableBody>
                  {tableData.length > 0 &&
                    tableData.map((row) => (
                      <BookRequestsTableRow
                        key={row.id}
                        row={row}
                        onAccept={handleAcceptRequest}
                        onReject={handleRejectRequest}
                        onDelete={handleDeleteRequest}
                      />
                    ))}

                  <TableEmptyRows
                    height={denseHeight}
                    emptyRows={emptyRows(
                      tableConfig.page,
                      tableConfig.rowsPerPage,
                      tableData.length
                    )}
                  />

                  <TableNoData
                    show={emptyData}
                    title={isFiltered ? 'No requests match your search' : 'No requests found'}
                    subTitle={isFiltered ? 'Try adjusting your search or filters' : undefined}
                  />
                </TableBody>
              </Table>
            </Scrollbar>
          </TableContainer>

          <TablePaginationCustom
            count={totalCount}
            page={tableConfig.page}
            rowsPerPage={tableConfig.rowsPerPage}
            onPageChange={tableConfig.onChangePage}
            onRowsPerPageChange={tableConfig.onChangeRowsPerPage}
            dense={tableConfig.dense}
            onChangeDense={tableConfig.onChangeDense}
          />
        </Card>
      ) : (
        <EmptyContent
          title={isFiltered ? 'No requests match your search' : 'No book requests yet'}
          description={
            isFiltered
              ? "Try adjusting your search terms or filters to find what you're looking for."
              : 'Book requests will appear here when users request new books.'
          }
          imgUrl="/assets/icons/empty/ic_secondary_shape.svg"
        />
      )}
    </>
  );
}
