import type { IBooksTableFilters } from 'src/types/books';
import type { SelectChangeEvent } from '@mui/material/Select';

import { useCallback } from 'react';

import Stack from '@mui/material/Stack';
import Select from '@mui/material/Select';
import MenuItem from '@mui/material/MenuItem';
import TextField from '@mui/material/TextField';
import InputLabel from '@mui/material/InputLabel';
import FormControl from '@mui/material/FormControl';
import OutlinedInput from '@mui/material/OutlinedInput';
import InputAdornment from '@mui/material/InputAdornment';

import { useCategoryData } from 'src/hooks/useSupabase/useCategories';

import { Iconify } from 'src/components/iconify';

// ----------------------------------------------------------------------

type Props = {
  filters: IBooksTableFilters;
  onFilters: (
    name: keyof IBooksTableFilters,
    value: IBooksTableFilters[keyof IBooksTableFilters]
  ) => void;
};

export default function BooksTableToolbar({ filters, onFilters }: Props) {
  const { data: categoriesData } = useCategoryData('categories');
  const activeCategories = categoriesData?.filter((item: any) => item.is_status === true) ?? [];

  const handleFilterName = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      onFilters('searchKeyword', event.target.value);
    },
    [onFilters]
  );

  const handleFilterCategory = useCallback(
    (event: SelectChangeEvent<string>) => {
      onFilters('categoryId', event.target.value as string);
    },
    [onFilters]
  );

  // const handleFilterAvailability = useCallback(
  //   (event: React.ChangeEvent<HTMLInputElement>) => {
  //     const value = event.target.checked;
  //     onFilters('is_available', value);
  //   },
  //   [onFilters]
  // );

  // const handleFilterStatus = useCallback(
  //   (event: React.ChangeEvent<HTMLInputElement>) => {
  //     const value = event.target.checked;
  //     onFilters('is_active', value);
  //   },
  //   [onFilters]
  // );

  return (
    <Stack
      spacing={2}
      alignItems={{ xs: 'flex-end', md: 'center' }}
      direction={{
        xs: 'column',
        md: 'row',
      }}
      sx={{
        p: 2.5,
        pr: { xs: 2.5, md: 1 },
      }}
    >
      <Stack direction="row" alignItems="center" spacing={2} flexGrow={1} sx={{ width: 1 }}>
        <TextField
          fullWidth
          value={filters.searchKeyword}
          onChange={handleFilterName}
          placeholder="Search books, authors..."
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Iconify icon="eva:search-fill" sx={{ color: 'text.disabled' }} />
              </InputAdornment>
            ),
          }}
        />

        <FormControl sx={{ minWidth: 180 }}>
          <InputLabel>Category</InputLabel>
          <Select
            value={filters.categoryId || ''}
            onChange={handleFilterCategory}
            input={<OutlinedInput label="Category" />}
          >
            <MenuItem value="">All Categories</MenuItem>
            {activeCategories.map((category) => (
              <MenuItem key={category.id} value={category.id}>
                {category.name}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Stack>

      {/* <Stack direction="row" alignItems="center" spacing={1}>
        <FormControlLabel
          control={
            <Checkbox
              checked={filters.is_available === true}
              onChange={handleFilterAvailability}
              indeterminate={filters.is_available === undefined}
            />
          }
          label="Available Only"
        />

        <FormControlLabel
          control={
            <Checkbox
              checked={filters.is_active === true}
              onChange={handleFilterStatus}
              indeterminate={filters.is_active === undefined}
            />
          }
          label="Active Only"
        />
      </Stack> */}
    </Stack>
  );
}
