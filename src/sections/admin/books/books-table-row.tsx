import type { ChangeEvent } from 'react';
import type { IBooksTableData } from 'src/types/books';

import { useState } from 'react';

import Button from '@mui/material/Button';
import Tooltip from '@mui/material/Tooltip';
import TableRow from '@mui/material/TableRow';
import TableCell from '@mui/material/TableCell';
import IconButton from '@mui/material/IconButton';
import { Box, Switch, Typography } from '@mui/material';

import { useBoolean } from 'src/hooks/use-boolean';

import { lightenHexColor } from 'src/utils/lighten-hex-color';

import { Label } from 'src/components/label';
import { toast } from 'src/components/snackbar';
import { Iconify } from 'src/components/iconify';
import { ConfirmDialog } from 'src/components/custom-dialog';

type Props = {
  row: IBooksTableData;
  onEdit: () => void;
  onDelete: (id: string) => Promise<void>;
  onUpdateStatus: (id: string, status: boolean) => Promise<void>;
};

export default function BooksTableRow({ row, onEdit, onDelete, onUpdateStatus }: Props) {
  const { id, name, borrower, author, category, is_active } = row;

  const [activeStatus, setActiveStatus] = useState(is_active);

  const confirm = useBoolean();
  const isStatusToggling = useBoolean(false);

  const handleBookDeletion = async () => {
    confirm.onFalse();
    try {
      await onDelete(id);
    } catch (error) {
      console.error('Error deleting book:', error);
      toast.error('Failed to delete book. Please try again.');
    }
  };

  const handleStatusToggle = async (e: ChangeEvent<HTMLInputElement>) => {
    const status = e.currentTarget.checked;
    try {
      isStatusToggling.onTrue();
      await onUpdateStatus(id, status);
      setActiveStatus(status);
    } catch (error) {
      console.error('Error updating book status:', error);
      toast.error('Failed to update book status. Please try again.');
      setActiveStatus(!status);
    } finally {
      isStatusToggling.onFalse();
    }
  };

  return (
    <>
      <TableRow hover>
        <TableCell
          sx={{
            maxWidth: '310px',
          }}
        >
          <Typography
            variant="body2"
            sx={{
              fontWeight: '900',
              lineHeight: '22px',
              whiteSpace: 'nowrap',
              textOverflow: 'ellipsis',
              overflow: 'hidden',
            }}
          >
            {name}
          </Typography>

          <Typography
            variant="body2"
            sx={{ fontWeight: '400', lineHeight: '22px', color: 'grey.500' }}
          >
            {author}
          </Typography>
        </TableCell>

        <TableCell sx={{ maxWidth: '150px' }}>
          {category && (
            <Label
              variant="soft"
              sx={{
                backgroundColor: lightenHexColor(category?.color, 88),
                color: category?.color,
                fontWeight: '700',
                fontSize: '12px',
                lineHeight: '20px',
              }}
            >
              {category.name}
            </Label>
          )}
        </TableCell>

        <TableCell align="center" sx={{ px: 1, whiteSpace: 'nowrap' }}>
          {borrower === null && (
            <Iconify
              icon="mdi:check"
              width={24}
              sx={{
                color: 'success.main',
              }}
            />
          )}
        </TableCell>

        <TableCell align="center" sx={{ px: 1, whiteSpace: 'nowrap' }}>
          <Switch
            checked={activeStatus}
            color="success"
            onChange={handleStatusToggle}
            disabled={isStatusToggling.value}
          />
        </TableCell>

        <TableCell align="right" sx={{ px: 1, whiteSpace: 'nowrap' }}>
          <Tooltip title="Edit" placement="top" arrow>
            <IconButton onClick={onEdit}>
              <Iconify icon="solar:pen-bold" />
            </IconButton>
          </Tooltip>
        </TableCell>

        <TableCell align="right" sx={{ px: 1, whiteSpace: 'nowrap' }}>
          <Tooltip title="Delete" placement="top" arrow>
            <IconButton onClick={confirm.onTrue} sx={{ color: 'error.dark' }}>
              <Iconify icon="solar:trash-bin-trash-bold" />
            </IconButton>
          </Tooltip>
        </TableCell>
      </TableRow>

      <ConfirmDialog
        open={confirm.value}
        onClose={confirm.onFalse}
        title={
          <Box display="flex" gap={2} alignItems="center">
            <Iconify icon="solar:trash-bin-trash-bold" /> Delete Book
          </Box>
        }
        content={
          <>
            <Typography variant="subtitle1">Are you sure?</Typography>
            <Typography variant="body1">
              This book will be permanently removed from the library.
            </Typography>
          </>
        }
        action={
          <Button variant="contained" color="error" onClick={handleBookDeletion}>
            Delete
          </Button>
        }
      />
    </>
  );
}
