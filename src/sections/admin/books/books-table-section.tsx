import type { IBooksTableData, IBooksTableFilters } from 'src/types/books';

import { useState } from 'react';
import { isEqual } from 'lodash';

import Card from '@mui/material/Card';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableContainer from '@mui/material/TableContainer';

import { useDebounce } from 'src/hooks/use-debounce';

import { removeFalsyValuesFromObject } from 'src/utils/misc';

import { TABLE_DEFAULTS } from 'src/constants/table';
import { BOOKS_TABLE_COLUMNS, DEFAULT_BOOKS_TABLE_FILTERS } from 'src/constants/books';

import { Scrollbar } from 'src/components/scrollbar';
import { EmptyContent } from 'src/components/empty-content';
import {
  useTable,
  emptyRows,
  TableNoData,
  TableEmptyRows,
  TableHeadCustom,
  TablePaginationCustom,
} from 'src/components/table';

import BooksTableRow from './books-table-row';
import BooksTableToolbar from './books-table-toolbar';
import BooksTableFiltersResult from './books-table-filters-result';

type Props = {
  booksData: IBooksTableData[];
  loading: boolean;
  onEditBook: (book: IBooksTableData) => void;
  onDeleteBook: (id: string) => Promise<void>;
  onUpdateBookStatus: (id: string, status: boolean) => Promise<void>;
};

export default function BooksTableSection({
  booksData,
  loading,
  onEditBook,
  onDeleteBook,
  onUpdateBookStatus,
}: Props) {
  // -- Table state and config --
  const tableConfig = useTable();
  const [filters, setFilters] = useState(DEFAULT_BOOKS_TABLE_FILTERS);

  // -- Derived values --
  const denseHeight = tableConfig.dense
    ? TABLE_DEFAULTS.DENSE_HEIGHT
    : TABLE_DEFAULTS.DENSE_HEIGHT_WITH_MARGIN;

  const debouncedSearchQuery = useDebounce(filters.searchKeyword, 1500);

  // TODO: Replace with actual API call
  const data: IBooksTableData[] = booksData;

  // -- Apply filters to the data --
  const filteredData = data.filter((item: any) => {
    // Search filter - search in name, author, and description (case-insensitive, trimmed)
    const searchTerm = debouncedSearchQuery.trim().toLowerCase();
    const matchesSearch = searchTerm
      ? item.name.toLowerCase().includes(searchTerm) ||
        item.author.toLowerCase().includes(searchTerm) ||
        item.description.toLowerCase().includes(searchTerm)
      : true;

    // Category filter
    const matchesCategory = filters.categoryId ? item.category?.id === filters.categoryId : true;

    // Status filter
    const matchesStatus =
      filters.is_active !== undefined ? item.is_active === filters.is_active : true;

    return matchesSearch && matchesCategory && matchesStatus;
  });

  // -- Filter API payload --
  const apiFilters = removeFalsyValuesFromObject({
    ...filters,
    page: tableConfig.page + 1,
    pageSize: tableConfig.rowsPerPage,
    searchQuery: debouncedSearchQuery,
  });
  console.log('🚀 ~ apiFilters:', apiFilters);

  // -- Derived table data --
  const tableData = filteredData;
  const totalCount = filteredData.length;
  const isFetching = loading;

  // -- Computed helpers --
  const canReset = !isEqual(DEFAULT_BOOKS_TABLE_FILTERS, filters);
  const emptyData = !tableData.length;
  const hasData = data.length > 0;
  const isFiltered =
    debouncedSearchQuery.length > 0 || filters.categoryId || filters.is_active !== undefined;
  const shouldShowTable = isFetching || hasData;

  // -- Handlers --
  const handleFilters = <K extends keyof IBooksTableFilters>(
    name: K,
    value: IBooksTableFilters[K]
  ) => {
    tableConfig.onResetPage();
    setFilters((prevState) => ({
      ...prevState,
      [name]: value,
    }));
  };

  const handleResetFilters = () => {
    setFilters(DEFAULT_BOOKS_TABLE_FILTERS);
  };

  const handleDeleteBook = async (id: string) => {
    try {
      await onDeleteBook(id);
    } catch (error) {
      console.error('Error deleting book:', error);
    }
  };

  const handleUpdateBookStatus = async (id: string, status: boolean) => {
    try {
      await onUpdateBookStatus(id, status);
    } catch (error) {
      console.error('Error updating book status:', error);
    }
  };

  return (
    <>
      {shouldShowTable ? (
        <Card>
          <BooksTableToolbar filters={filters} onFilters={handleFilters} />

          {canReset && (
            <BooksTableFiltersResult
              filters={filters}
              onFilters={handleFilters}
              onResetFilters={handleResetFilters}
              results={totalCount}
              sx={{ p: 2.5, pt: 0 }}
            />
          )}

          <TableContainer sx={{ position: 'relative', overflow: 'unset' }}>
            <Scrollbar>
              <Table size={tableConfig.dense ? 'small' : 'medium'} sx={{ minWidth: 960 }}>
                <TableHeadCustom headLabel={BOOKS_TABLE_COLUMNS} />

                <TableBody>
                  {tableData.length > 0 &&
                    tableData.map((row: IBooksTableData) => (
                      <BooksTableRow
                        key={row.id}
                        row={row}
                        onEdit={() => onEditBook(row)}
                        onDelete={handleDeleteBook}
                        onUpdateStatus={handleUpdateBookStatus}
                      />
                    ))}

                  <TableEmptyRows
                    height={denseHeight}
                    emptyRows={emptyRows(
                      tableConfig.page,
                      tableConfig.rowsPerPage,
                      tableData.length
                    )}
                  />

                  <TableNoData
                    show={emptyData}
                    title={isFiltered ? 'No books match your search' : 'No books found'}
                    subTitle={isFiltered ? 'Try adjusting your search or filters' : undefined}
                  />
                </TableBody>
              </Table>
            </Scrollbar>
          </TableContainer>

          <TablePaginationCustom
            count={totalCount}
            page={tableConfig.page}
            rowsPerPage={tableConfig.rowsPerPage}
            onPageChange={tableConfig.onChangePage}
            onRowsPerPageChange={tableConfig.onChangeRowsPerPage}
            dense={tableConfig.dense}
            onChangeDense={tableConfig.onChangeDense}
          />
        </Card>
      ) : (
        <EmptyContent
          title={isFiltered ? 'No books match your search' : 'No books yet'}
          description={
            isFiltered
              ? "Try adjusting your search terms or filters to find what you're looking for."
              : 'Get started by adding your first book to the library.'
          }
          imgUrl="/assets/icons/empty/ic_secondary_shape.svg"
        />
      )}
    </>
  );
}
