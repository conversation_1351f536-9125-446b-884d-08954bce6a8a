import type { IStation } from 'src/types/stations';
import type { IUseBooleanReturn } from 'src/hooks/use-boolean';
import type { IStationEditCreateSchema } from 'src/schema/station';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';

import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import Typography from '@mui/material/Typography';
import IconButton from '@mui/material/IconButton';
import LoadingButton from '@mui/lab/LoadingButton';
import FormControl from '@mui/material/FormControl';
import DialogTitle from '@mui/material/DialogTitle';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';

import { createStation } from 'src/hooks/useSupabase/useStations';

import { StationEditCreateSchema } from 'src/schema/station';

import { toast } from 'src/components/snackbar';
import { Iconify } from 'src/components/iconify';
import { RHFTextField } from 'src/components/hook-form';
import { Form } from 'src/components/hook-form/form-provider';
import { RHFSwitch } from 'src/components/hook-form/rhf-switch';

interface ShelfNewDialogProps {
  dialog: IUseBooleanReturn;
  onCreateStation: (shelfData: IStation) => Promise<void>;
}

const defaultValues: IStationEditCreateSchema = {
  name: '',
  location: '',
  isActive: true,
};

export default function StationNewDialog({ dialog, onCreateStation }: ShelfNewDialogProps) {
  const methods = useForm<IStationEditCreateSchema>({
    resolver: zodResolver(StationEditCreateSchema),
    defaultValues,
  });

  const {
    handleSubmit,
    formState: { isSubmitting },
    reset,
  } = methods;

  const handleCloseDialog = () => {
    reset();
    dialog.onFalse();
  };

  const onSubmit = handleSubmit(async (data) => {
    try {
      const insertedData = await createStation(data);
      if (!insertedData) throw new Error('No station returned from API');

      await onCreateStation(insertedData);

      handleCloseDialog();
      toast.success('Station created successfully!');
    } catch (err: any) {
      toast.error(err?.message || 'Error creating station');
    }
  });

  return (
    <Dialog
      open={dialog.value}
      onClose={() => {
        if (!isSubmitting) handleCloseDialog();
      }}
      fullWidth
      maxWidth="sm"
      sx={{
        '& .MuiDialog-paper': {
          borderRadius: 2,
        },
      }}
    >
      <Form methods={methods} onSubmit={onSubmit}>
        <DialogTitle>
          <Typography variant="h6" component="span" fontWeight="bold">
            Create New Station
          </Typography>
        </DialogTitle>

        <IconButton
          aria-label="close dialog"
          disabled={isSubmitting}
          onClick={handleCloseDialog}
          sx={{
            position: 'absolute',
            right: 12,
            top: 12,
            color: (theme) => theme.palette.grey[500],
            '&:hover': {
              color: (theme) => theme.palette.grey[700],
            },
          }}
        >
          <Iconify icon="mingcute:close-line" />
        </IconButton>

        <DialogContent
          sx={{
            display: 'flex',
            flexDirection: 'column',
            gap: 3,
            pt: 2,
          }}
        >
          {/* Station Name Field */}
          <RHFTextField
            name="name"
            label="Station Name"
            placeholder="Enter station name (e.g., Fiction Books, Reference Materials)"
            size="small"
            fullWidth
            helperText="A descriptive name to identify this station"
          />

          {/* Station Location Field */}
          <RHFTextField
            name="location"
            label="Station Location"
            placeholder="Enter physical location (e.g., Room A - Wall 1, Section B)"
            size="small"
            fullWidth
            helperText="Physical location or position of this station"
          />

          {/* Status Switch */}
          <FormControl>
            <Typography variant="subtitle2" fontWeight="fontWeightBold" sx={{ mb: 1 }}>
              Shelf Status
            </Typography>
            <RHFSwitch
              name="isActive"
              label="Enable this station for use"
              helperText="When enabled, this station will be available for public to view"
            />
          </FormControl>
        </DialogContent>

        <DialogActions sx={{ px: 3, pb: 3 }}>
          <Button
            onClick={handleCloseDialog}
            variant="outlined"
            color="inherit"
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <LoadingButton
            type="submit"
            variant="contained"
            color="primary"
            loading={isSubmitting}
            loadingPosition="start"
            startIcon={<Iconify icon="mingcute:add-line" />}
          >
            Create Shelf
          </LoadingButton>
        </DialogActions>
      </Form>
    </Dialog>
  );
}
