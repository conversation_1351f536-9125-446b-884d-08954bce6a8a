import type { ChangeEvent } from 'react';
import type { ITestTableItem } from 'src/types/books';

import { useState } from 'react';

import Button from '@mui/material/Button';
import Tooltip from '@mui/material/Tooltip';
import TableRow from '@mui/material/TableRow';
import TableCell from '@mui/material/TableCell';
import IconButton from '@mui/material/IconButton';
import { Box, Link, Switch, Typography, FormControlLabel } from '@mui/material';

import { paths } from 'src/routes/paths';
import { RouterLink } from 'src/routes/components';

import { useBoolean } from 'src/hooks/use-boolean';

import { lightenHexColor } from 'src/utils/lighten-hex-color';

// import { archiveTest, updateTestPublishStatus } from 'src/api/staff/tests';

import { Label } from 'src/components/label';
import { Iconify } from 'src/components/iconify';
import { ConfirmDialog } from 'src/components/custom-dialog';

// ----------------------------------------------------------------------

type Props = {
  row: ITestTableItem;
};

export default function TestsTableRow({ row }: Props) {
  const {
    name,
    totalQuestions,
    isPublished,
    category,
    lowerThreshold,
    nextTestId,
    prevTestId,
    isPrimary,
    totalResults,
  } = row;

  const [activeStatus, setActiveStatus] = useState(isPublished);

  const confirm = useBoolean();
  const shareDialog = useBoolean(false);

  const testIsPrimary =
    isPrimary || (lowerThreshold === null && prevTestId === null && nextTestId === null);

  const handleTestDeletion = async () => {
    confirm.onFalse();

    if (category?.name) {
      // enqueueSnackbar(
      //   t('test_listing_page.test_associated_with_category_delete', { name: category.name }),
      //   {
      //     variant: 'error',
      //   }
      // );
    }
    // await archiveTest(id!);
    // enqueueSnackbar(t('test_listing_page.test_deleted'));
  };

  const isLiveToggling = useBoolean(false);

  const handleLiveToggle = async (e: ChangeEvent<HTMLInputElement>) => {
    const status = e.currentTarget.checked;
    try {
      if (!status && category && category.isPublished) {
        // enqueueSnackbar();
        // t('test_edit_page.test_cannot_unpublish', { categoryName: category.name }),
        // {
        //   variant: 'error',
        // }
        return;
      }

      isLiveToggling.onTrue();
      // await updateTestPublishStatus(id, { status });
      setActiveStatus(status);
      isLiveToggling.onFalse();
      // queryClient.invalidateQueries({ queryKey: [keys.staff.tests.fetchTests] });
      // enqueueSnackbar(
      //   t('test_listing_page.test_status', { name, status: status ? 'published' : 'unpublished' }),
      //   {
      //     variant: 'success',
      //   }
      // );
    } catch (error) {
      // enqueueSnackbar(error?.message ? error?.message : JSON.stringify(error || '{}'), {
      //   variant: 'error',
      // });
      isLiveToggling.onFalse();
    }
  };

  const handleShare = async () => {
    shareDialog.onTrue();
  };

  const redirectUrl = paths.dashboard.books;

  return (
    <>
      <TableRow hover>
        <TableCell sx={{ whiteSpace: 'nowrap' }}>
          <Typography
            variant="body2"
            sx={{ fontWeight: '900', lineHeight: '22px', color: '#000000', cursor: 'pointer' }}
          >
            <Link color="inherit" underline="hover" component={RouterLink} href={redirectUrl}>
              {name}
            </Link>
          </Typography>

          <Typography
            variant="body2"
            sx={{ fontWeight: '400', lineHeight: '22px', color: 'grey.500' }}
          >
            {totalResults} Results
          </Typography>
        </TableCell>

        <TableCell sx={{ whiteSpace: 'nowrap' }}>
          {category && (
            <Label
              variant="soft"
              sx={{
                backgroundColor: lightenHexColor(category.color, 88),
                color: category.color,
                fontWeight: '700',
                fontSize: '12px',
                lineHeight: '20px',
              }}
            >
              {category && category.name}
            </Label>
          )}
        </TableCell>

        <TableCell sx={{ whiteSpace: 'nowrap' }}>
          {testIsPrimary ? <Iconify icon="material-symbols:check" color="success.main" /> : ''}
        </TableCell>

        <TableCell sx={{ whiteSpace: 'nowrap' }}>
          <Typography variant="body2" sx={{ fontWeight: '400', lineHeight: '22px' }}>
            {totalQuestions}
          </Typography>
        </TableCell>

        <TableCell sx={{ px: 1, whiteSpace: 'nowrap' }}>
          <FormControlLabel
            control={<Switch checked={activeStatus} color="success" onChange={handleLiveToggle} />}
            label="Live"
          />
        </TableCell>

        <TableCell sx={{ px: 1, whiteSpace: 'nowrap' }}>
          <Tooltip title="View" placement="top" arrow>
            <IconButton onClick={confirm.onTrue} sx={{ color: 'error.dark' }}>
              <Iconify icon="solar:eye-bold" />
            </IconButton>
          </Tooltip>
        </TableCell>

        <TableCell sx={{ px: 1, whiteSpace: 'nowrap' }}>
          <Tooltip title="Delete" placement="top" arrow>
            <IconButton onClick={confirm.onTrue} sx={{ color: 'error.dark' }}>
              <Iconify icon="solar:trash-bin-trash-bold" />
            </IconButton>
          </Tooltip>
        </TableCell>
      </TableRow>

      <ConfirmDialog
        open={confirm.value}
        onClose={confirm.onFalse}
        title={
          <Box display="flex" gap={2} alignItems="center">
            <Iconify icon="solar:trash-bin-trash-bold" /> Delete Book
          </Box>
        }
        content={
          <>
            <Typography variant="subtitle1">Are you sure ?</Typography>
            <Typography variant="body1">All text book under this will also hidden</Typography>
          </>
        }
        action={
          <Button variant="contained" color="error" onClick={handleTestDeletion}>
            Delete
          </Button>
        }
      />
    </>
  );
}
