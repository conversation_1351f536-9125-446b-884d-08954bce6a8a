import type { IStation } from 'src/types/stations';

import { useState } from 'react';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import { LoadingButton } from '@mui/lab';
import Avatar from '@mui/material/Avatar';
import Divider from '@mui/material/Divider';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import { Link, Stack, Alert, Switch, FormControlLabel } from '@mui/material';

import { paths } from 'src/routes/paths';
import { RouterLink } from 'src/routes/components';

import { useBoolean } from 'src/hooks/use-boolean';

import { Iconify } from 'src/components/iconify';
import { ConfirmDialog } from 'src/components/custom-dialog';

type Props = {
  station: IStation;
  handleToggleActive: (id: string, isActive: IStation['isActive']) => Promise<void>;
  onDelete: (id: string) => Promise<void>;
};

export default function StationCard({ station, handleToggleActive, onDelete }: Props) {
  const { id, name, isActive, bookCount } = station;

  const [isDeleting, setIsDeleting] = useState(false);
  const [isStatusToggling, setIsStatusToggling] = useState(false);

  const confirm = useBoolean();

  const redirectUrl = paths.dashboard.stations.detail(id);

  const handleDeleteStation = async () => {
    setIsDeleting(true);
    await onDelete(id);
    setIsDeleting(false);
  };

  return (
    <>
      <Card
        sx={{
          padding: 3,
        }}
      >
        <Box gap={2} display="flex" justifyContent="space-between">
          {/* Left section */}
          <Box gap={2} display="flex" flex={1} minWidth={0}>
            <Avatar alt={name} sx={{ width: 48, height: 48 }}>
              {name.charAt(0).toUpperCase()}
            </Avatar>
            <Stack sx={{ minWidth: 0 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', minWidth: 0 }}>
                <Typography
                  sx={{
                    textTransform: 'capitalize',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    minWidth: 0,
                  }}
                  fontWeight="bold"
                  variant="subtitle1"
                >
                  <Link color="inherit" underline="hover" component={RouterLink} href={redirectUrl}>
                    {name}
                  </Link>
                </Typography>
              </Box>
              <Box
                display="flex"
                gap="4px"
                alignItems="center"
                sx={{ color: (theme) => theme.palette.grey[500] }}
              >
                <Iconify width="16" height="16" icon="solar:notes-bold" />
                <Typography variant="body1" fontSize="14px">
                  {bookCount} books
                </Typography>
              </Box>
            </Stack>
          </Box>

          {/* Right section */}
          <Box display="flex" alignItems="center" gap={1} flexShrink={0}>
            <IconButton LinkComponent={RouterLink} href={redirectUrl}>
              <Iconify icon="solar:pen-bold" />
            </IconButton>
            <IconButton onClick={confirm.onTrue} sx={{ color: 'error.dark' }}>
              <Iconify icon="solar:trash-bin-trash-bold" />
            </IconButton>
          </Box>
        </Box>

        <Divider sx={{ my: 2, mx: -3, borderStyle: 'dashed' }} />

        <FormControlLabel
          control={
            <Switch
              disabled={isStatusToggling}
              checked={isActive}
              onChange={async (e) => {
                setIsStatusToggling(true);
                await handleToggleActive(id, e.target.checked);
                setIsStatusToggling(false);
              }}
              color="success"
            />
          }
          sx={{
            userSelect: 'none',
          }}
          label="Active"
        />
      </Card>

      <ConfirmDialog
        open={confirm.value}
        onClose={confirm.onFalse}
        title={
          <Stack spacing={2}>
            {bookCount > 0 && (
              <Alert severity="error" variant="standard">
                {`This station currently contains ${bookCount} book${bookCount > 1 ? 's' : ''}. Please move or delete them before deleting the station.`}
              </Alert>
            )}

            <Stack direction="row" spacing={1.5} alignItems="center">
              <Iconify
                icon="solar:trash-bin-trash-bold"
                width={24}
                height={24}
                color="error.main"
              />
              <Typography variant="h6" fontWeight="bold">
                Delete Station
              </Typography>
            </Stack>
          </Stack>
        }
        content={
          <Stack spacing={1}>
            <Typography variant="subtitle1" fontWeight={600}>
              {`Are you sure you want to delete "${name.toUpperCase()}"?`}
            </Typography>

            <Typography variant="body2" color="text.secondary">
              This action cannot be undone. All associated data will be removed.
            </Typography>
          </Stack>
        }
        action={
          <LoadingButton
            loading={isDeleting}
            variant="contained"
            color="error"
            disabled={bookCount > 0}
            onClick={handleDeleteStation}
          >
            Delete
          </LoadingButton>
        }
      />
    </>
  );
}
