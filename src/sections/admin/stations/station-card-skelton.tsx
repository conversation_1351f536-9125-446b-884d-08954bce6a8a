import React from 'react';

import {
  <PERSON>,
  <PERSON>,
  <PERSON>ack,
  Switch,
  <PERSON><PERSON><PERSON>,
  Skeleton,
  IconButton,
  Typography,
  FormControlLabel,
} from '@mui/material';

const ShelfCardSkelton = () => (
  <Card
    sx={{
      padding: 3,
    }}
  >
    <Box gap={2} display="flex" justifyContent="space-between">
      <Box gap={2} display="flex">
        {/* Avatar Shimmer */}
        <Skeleton variant="circular" width={48} height={48} />

        <Stack>
          {/* Name Shimmer */}
          <Typography
            sx={{
              flexGrow: 1,
              textTransform: 'capitalize',
            }}
            fontWeight="bold"
            textOverflow="ellipsis"
            whiteSpace="nowrap"
            overflow="hidden"
            variant="subtitle1"
          >
            <Skeleton width={120} height={24} />
          </Typography>

          {/* Book count section shimmer */}
          <Box
            display="flex"
            gap="4px"
            alignItems="center"
            sx={{
              color: (theme) => theme.palette.grey[500],
            }}
          >
            <Skeleton variant="circular" width={16} height={16} />
            <Typography variant="body1" fontSize="14px">
              <Skeleton width={60} height={20} />
            </Typography>
          </Box>
        </Stack>
      </Box>

      {/* Action buttons shimmer */}
      <Box display="flex" alignItems="center">
        <IconButton disabled>
          <Skeleton variant="circular" width={24} height={24} />
        </IconButton>
        <IconButton disabled>
          <Skeleton variant="circular" width={24} height={24} />
        </IconButton>
      </Box>
    </Box>

    {/* Divider - keeping original */}
    <Divider sx={{ my: 2, mx: -3, borderStyle: 'dashed' }} />

    {/* Switch section shimmer */}
    <FormControlLabel
      control={<Switch disabled color="success" />}
      sx={{
        userSelect: 'none',
      }}
      label={<Skeleton width={50} height={20} />}
    />
  </Card>
);

export default ShelfCardSkelton;
