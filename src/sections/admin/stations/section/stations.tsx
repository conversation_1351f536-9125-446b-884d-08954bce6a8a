import type { IStation } from 'src/types/stations';

import { deleteStation, updateStation } from 'src/hooks/useSupabase/useStations';

import { toast } from 'src/components/snackbar';

import StationCard from '../station-card';
import ListHOC from '../station-card-list-wrapper-hoc';

type Props = {
  stations: IStation[];
  onUpdateActiveStatus: (id: string, isActive: IStation['isActive']) => Promise<void>;
  onDelete: (id: string) => Promise<void>;
};

export const StationsList = ({ stations, onUpdateActiveStatus, onDelete }: Props) => {
  const handleUpdateActiveStatus = async (id: string, isActive: IStation['isActive']) => {
    try {
      await updateStation(id, { isActive });
      onUpdateActiveStatus(id, isActive);

      toast.success('Station status updated!');
    } catch (err: any) {
      toast.error(err?.message || 'Error updating station');
    }
  };

  const handleDelete = async (id: string) => {
    try {
      await deleteStation(id);
      onDelete(id);
      toast.success('Station deleted!');
    } catch (err: any) {
      toast.error(err.message || 'Error deleting station');
    }
  };

  return (
    <ListHOC>
      {stations.map((station) => (
        <StationCard
          key={station.id}
          station={station}
          handleToggleActive={handleUpdateActiveStatus}
          onDelete={handleDelete}
        />
      ))}
    </ListHOC>
  );
};
