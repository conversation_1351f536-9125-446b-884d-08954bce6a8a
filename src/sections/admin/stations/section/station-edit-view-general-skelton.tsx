import {
  Box,
  Paper,
  Stack,
  Button,
  Switch,
  Skeleton,
  Typography,
  IconButton,
  FormControl,
  FormControlLabel,
} from '@mui/material';

const ShelfEditViewGeneralSkeleton = () => (
  <>
    {/* Main Form Paper */}
    <Paper elevation={3} variant="elevation" sx={{ p: 3, my: 5 }}>
      <Stack
        sx={{
          display: 'flex',
          flexDirection: 'column',
          gap: 3,
        }}
      >
        {/* Header with title and QR button */}
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="subtitle2" fontWeight="fontWeightBold">
            Station Details
          </Typography>
          <IconButton disabled color="primary">
            <Skeleton variant="circular" width={24} height={24} />
          </IconButton>
        </Box>

        {/* Shelf Name Field */}
        <Box>
          <Skeleton variant="rounded" width="100%" height={40} sx={{ borderRadius: 1 }} />
          <Typography variant="caption" sx={{ mt: 0.5, display: 'block' }}>
            <Skeleton width={200} height={16} />
          </Typography>
        </Box>

        {/* Shelf Location Field */}
        <Box>
          <Skeleton variant="rounded" width="100%" height={40} sx={{ borderRadius: 1 }} />
          <Typography variant="caption" sx={{ mt: 0.5, display: 'block' }}>
            <Skeleton width={180} height={16} />
          </Typography>
        </Box>

        {/* Shelf Status Section */}
        <FormControl>
          <Typography variant="subtitle2" fontWeight="fontWeightBold" sx={{ mb: 1 }}>
            Station Status
          </Typography>
          <FormControlLabel
            control={<Switch disabled checked color="success" />}
            sx={{
              userSelect: 'none',
            }}
            label={
              <Box>
                <Typography variant="body2">
                  <Skeleton width={160} height={20} />
                </Typography>
              </Box>
            }
          />
        </FormControl>
        <Typography variant="caption" color="text.secondary">
          <Skeleton width={220} height={16} />
        </Typography>
      </Stack>
    </Paper>

    {/* Action Buttons */}
    <Stack justifyContent="end" flexDirection="row" gap={2}>
      <Button variant="outlined" color="inherit" disabled>
        <Skeleton width={60} height={20} />
      </Button>
      <Button variant="contained" color="primary" disabled>
        <Skeleton width={100} height={20} />
      </Button>
    </Stack>
  </>
);

export default ShelfEditViewGeneralSkeleton;
