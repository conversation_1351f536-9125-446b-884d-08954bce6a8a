import { isEqual } from 'lodash';
import { useMemo, useState, useCallback } from 'react';

import { Card, Table, TableBody, TableContainer } from '@mui/material';

import { useBoolean } from 'src/hooks/use-boolean';

// import { useGetTests } from 'src/api/staff/tests';

import type { IBook, IBookTableFilter, IBookTableFilterValue } from 'src/types/books';

import { useBooksList } from 'src/hooks/useSupabase/useBooks';

import { Scrollbar } from 'src/components/scrollbar';
import { EmptyContent } from 'src/components/empty-content/empty-content';
import {
  useTable,
  emptyRows,
  TableNoData,
  TableEmptyRows,
  TableHeadCustom,
  TablePaginationCustom,
} from 'src/components/table';

import TestsTableRow from '../test-table-row';
import TestNewDialog from '../test-new-dialog';
import TestsTableToolbar from '../test-table-toolbar';
import TestTableFiltersResult from '../test-table-filters-result';

const defaultFilters: IBookTableFilter = {
  searchQuery: '',
};

function StationEditViewBooks({ stationId }: { stationId: string }) {
  const newTestDialog = useBoolean();

  const TABLE_HEAD = useMemo(
    () => [
      { id: 'name', label: 'Name', width: 300, align: 'left' },
      { id: 'category', label: 'Category', width: 220, align: 'left' },
      { id: 'availability', label: 'Availability', width: 132, align: 'left' },
      { id: 'isActive', label: 'status', width: 50, align: 'right' },
      { id: 'delete', label: '', width: 50, align: 'right' },
      { id: 'menu', label: '', width: 50, align: 'right' },
    ],
    []
  );

  const table = useTable();

  const [filters, setFilters] = useState(defaultFilters);

  const canReset = !isEqual(defaultFilters, filters);

  const denseHeight = table.dense ? 56 : 56 + 20;
  const { data, isLoading, error, refetch } = useBooksList({ stationId });

  const {
    tableData = [],
    totalCount = 0,
  }: {
    tableData: IBook[];
    totalCount: number;
  } = useMemo(
    () => ({
      tableData: data,
      totalCount: data.length,
    }),
    [data]
  );

  const notFound = (!tableData.length && canReset) || !tableData.length;

  const handleFilters = useCallback(
    (name: string, value: IBookTableFilterValue) => {
      table.onResetPage();
      setFilters((prevState) => ({
        ...prevState,
        [name]: value,
      }));
    },
    [table]
  );

  const handleResetFilters = useCallback(() => {
    setFilters(defaultFilters);
  }, []);

  return (
    <>
      <Card>
        {isLoading ||
        // debouncedsearchQuery.length > 0 ||
        canReset ||
        tableData.length ? (
          <>
            <TestsTableToolbar filters={filters} onFilters={handleFilters} />

            {canReset && (
              <TestTableFiltersResult
                filters={filters}
                onFilters={handleFilters}
                onResetFilters={handleResetFilters}
                results={totalCount}
                sx={{ p: 2.5, pt: 0 }}
              />
            )}

            <TableContainer sx={{ position: 'relative', overflow: 'unset' }}>
              <Scrollbar>
                <Table size={table.dense ? 'small' : 'medium'} sx={{ minWidth: 960 }}>
                  <TableHeadCustom
                    // order={table.order}
                    // orderBy={table.orderBy}
                    headLabel={TABLE_HEAD}
                    rowCount={20}
                    numSelected={table.selected.length}
                    onSort={table.onSort}
                    sx={{ whiteSpace: 'nowrap' }}
                  />

                  <TableBody>
                    {tableData.length > 0 &&
                      tableData.map((row: any) => <TestsTableRow key={row.id} row={row} />)}

                    <TableEmptyRows
                      height={denseHeight}
                      emptyRows={emptyRows(table.page, table.rowsPerPage, tableData.length)}
                    />

                    <TableNoData subTitle="" title="" show={notFound} />
                  </TableBody>
                </Table>
              </Scrollbar>
            </TableContainer>

            <TablePaginationCustom
              count={totalCount}
              page={table.page}
              rowsPerPage={table.rowsPerPage}
              onPageChange={table.onChangePage}
              onRowsPerPageChange={table.onChangeRowsPerPage}
              dense={table.dense}
              onChangeDense={table.onChangeDense}
            />
          </>
        ) : (
          <EmptyContent
            title="No books are there!"
            imgUrl="/assets/icons/empty/ic_secondary_shape.svg"
            actionButton={{
              label: 'Add One',
              onClick: newTestDialog.onTrue,
            }}
          />
        )}
      </Card>
      {newTestDialog.value && (
        <TestNewDialog
        // dialog={newTestDialog}
        // testDuplicationId={testDuplicationId}
        // setTestDuplicationId={setTestDuplicationId}
        />
      )}
    </>
  );
}

export default StationEditViewBooks;
