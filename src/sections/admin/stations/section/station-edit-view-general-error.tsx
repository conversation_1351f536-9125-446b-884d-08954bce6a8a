import type { ButtonProps } from '@mui/material';
import type { StackProps } from '@mui/material/Stack';

import Stack from '@mui/material/Stack';
import { alpha } from '@mui/material/styles';
import { Paper, Button } from '@mui/material';
import Typography from '@mui/material/Typography'; // Adjust import path as needed
import { Iconify } from 'src/components/iconify';

// ----------------------------------------------------------------------

export interface ShelfEditViewGeneralErrorProps extends StackProps {
  // Error Configuration
  error: string;
  title?: string;
  description?: string;
  imgUrl?: string;

  // Action Buttons Configuration
  retryButton?: {
    label?: string;
    onClick: () => void;
    buttonProps?: Omit<ButtonProps, 'onClick' | 'children'>;
  };

  backButton?: {
    label?: string;
    onClick?: () => void;
    buttonProps?: Omit<ButtonProps, 'onClick' | 'children'>;
  };
}

const ShelfEditViewGeneralError = ({
  error,
  title = 'Failed to Load Shelf Details',
  description,
  imgUrl,
  retryButton,
  backButton = {
    label: 'Go Back',
    onClick: () => window.history.back(),
  },
  sx,
  ...other
}: ShelfEditViewGeneralErrorProps) => {
  // Extract error message with fallback
  const errorMessage = error ?? 'An unexpected error occurred';

  // Use custom description or fallback to error message
  const displayDescription = description || `Error: ${errorMessage}`;

  return (
    <Paper elevation={3} variant="elevation" sx={{ p: 3, my: 5 }}>
      <Stack
        flexGrow={1}
        alignItems="center"
        justifyContent="center"
        sx={{
          height: 312,
          borderRadius: 1,
          bgcolor: (theme) => alpha(theme.palette.error.main, 0.04),
          border: (theme) => `dashed 1px ${alpha(theme.palette.error.main, 0.08)}`,
          p: 3,
          ...sx,
        }}
        {...other}
      >
        {title && (
          <Typography
            variant="h6"
            component="span"
            sx={{
              mt: 1,
              color: 'error.main',
              textAlign: 'center',
              fontWeight: 'medium',
            }}
          >
            {title}
          </Typography>
        )}

        {displayDescription && (
          <Typography
            variant="caption"
            sx={{
              mt: 1,
              color: 'text.disabled',
              textAlign: 'center',
              maxWidth: 400,
            }}
          >
            {displayDescription}
          </Typography>
        )}

        {/* Action Buttons */}
        <Stack direction="row" spacing={1.5} sx={{ mt: 2 }}>
          {retryButton && (
            <Button
              color="primary"
              variant="contained"
              onClick={retryButton.onClick}
              startIcon={<Iconify icon="solar:refresh-bold" />}
              {...retryButton.buttonProps}
            >
              {retryButton.label || 'Try Again'}
            </Button>
          )}

          {backButton && (
            <Button
              color="inherit"
              variant="outlined"
              onClick={backButton.onClick}
              startIcon={<Iconify icon="carbon:chevron-left" />}
              {...backButton.buttonProps}
            >
              {backButton.label || 'Go Back'}
            </Button>
          )}
        </Stack>
      </Stack>
    </Paper>
  );
};

export default ShelfEditViewGeneralError;
