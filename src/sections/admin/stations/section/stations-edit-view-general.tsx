import type { IStationData } from 'src/types/stations';

import { QRCodeSVG } from 'qrcode.react';
import { useForm } from 'react-hook-form';
import { useMemo, useEffect } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';

import { LoadingButton } from '@mui/lab';
import {
  Box,
  Paper,
  Stack,
  Button,
  Dialog,
  Typography,
  IconButton,
  FormControl,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';

import { paths } from 'src/routes/paths';

import { useBoolean } from 'src/hooks/use-boolean';
import { updateStation } from 'src/hooks/useSupabase/useStations';

import { CONFIG } from 'src/config-global';
import { StationEditCreateSchema, type IStationEditCreateSchema } from 'src/schema/station';

import { toast } from 'src/components/snackbar';
import { Iconify } from 'src/components/iconify';
import { RHFTextField } from 'src/components/hook-form';
import { Form } from 'src/components/hook-form/form-provider';
import { RHFSwitch } from 'src/components/hook-form/rhf-switch';

const defaultValues = {
  name: '',
  location: '',
  isActive: true,
};

function StationEditViewGeneral({
  station,
  onUpdateStation,
}: {
  station: IStationData;
  onUpdateStation: (updates: Omit<IStationData, 'id'>) => Promise<void>;
}) {
  const qrDialog = useBoolean();

  const methods = useForm<IStationEditCreateSchema>({
    resolver: zodResolver(StationEditCreateSchema),
    defaultValues,
  });

  const {
    handleSubmit,
    reset,
    formState: { isSubmitting, isDirty },
  } = methods;

  useEffect(() => {
    reset(station);
  }, [reset, station]);

  const onSubmit = handleSubmit(async (data: IStationEditCreateSchema) => {
    try {
      await updateStation(station.id, data);
      toast.success('Station updated successfully!');
      reset(data);
      onUpdateStation(data);
    } catch (error: any) {
      console.error('Error updating station:', error);
      toast.error(error?.message || 'Failed to update station');
    }
  });

  const qrCodeValue = useMemo(
    () => `${CONFIG.site.basePath}${paths.public.station.books(station?.id ?? '')}`,
    [station]
  );

  return (
    <>
      <Form methods={methods} onSubmit={onSubmit}>
        <Paper elevation={3} variant="elevation" sx={{ p: 3, my: 5 }}>
          <Stack
            sx={{
              display: 'flex',
              flexDirection: 'column',
              gap: 3,
            }}
          >
            <Box display="flex" justifyContent="space-between" alignItems="center">
              <Typography variant="subtitle2" fontWeight="fontWeightBold">
                Station Details
              </Typography>
              <IconButton onClick={qrDialog.onTrue} color="primary">
                <Iconify icon="solar:qr-code-bold" />
              </IconButton>
            </Box>

            <RHFTextField
              name="name"
              label="Station name"
              placeholder="Enter station name (e.g., Fiction Books, Reference Materials)"
              size="small"
              fullWidth
              helperText="A descriptive name to identify this station"
            />

            <RHFTextField
              name="location"
              label="Station Location"
              placeholder="Enter physical location (e.g., Room A - Wall 1, Section B)"
              size="small"
              fullWidth
              helperText="Physical location or position of this station"
            />

            <FormControl>
              <Typography variant="subtitle2" fontWeight="fontWeightBold" sx={{ mb: 1 }}>
                Station Status
              </Typography>
              <RHFSwitch
                name="isActive"
                label="Enable this station for use"
                helperText="When enabled, this station will be available for organizing items. Disabled stations are hidden from selection."
              />
            </FormControl>
          </Stack>
        </Paper>

        <Stack justifyContent="end" flexDirection="row" gap={2}>
          <Button variant="outlined" color="inherit" onClick={() => reset()}>
            Cancel
          </Button>
          <LoadingButton
            type="submit"
            variant="contained"
            color="primary"
            disabled={!isDirty}
            loading={isSubmitting}
          >
            Save Changes
          </LoadingButton>
        </Stack>
      </Form>

      {/* QR Code Dialog */}
      <Dialog
        open={qrDialog.value}
        onClose={qrDialog.onFalse}
        maxWidth="sm"
        fullWidth
        sx={{
          '& .MuiDialog-paper': {
            borderRadius: 2,
          },
        }}
      >
        <DialogTitle>
          <Typography variant="h6" component="span" fontWeight="bold">
            Station QR Code
          </Typography>
        </DialogTitle>

        <IconButton
          aria-label="close dialog"
          onClick={qrDialog.onFalse}
          sx={{
            position: 'absolute',
            right: 12,
            top: 12,
            color: (theme) => theme.palette.grey[500],
          }}
        >
          <Iconify icon="mingcute:close-line" />
        </IconButton>

        <DialogContent sx={{ pt: 2 }}>
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              gap: 2,
            }}
          >
            <Box
              sx={{
                p: 2,
                bgcolor: 'background.paper',
                borderRadius: 1,
                boxShadow: (theme) => theme.customShadows?.z8,
              }}
            >
              <QRCodeSVG
                value={qrCodeValue}
                size={256}
                level="H"
                includeMargin
                imageSettings={{
                  src: '/logo/logo-book.png', // TODO: Add your logo
                  height: 40,
                  width: 40,
                  excavate: true,
                }}
              />
            </Box>
            <Typography variant="body2" color="text.secondary" align="center">
              Scan this QR code to access the station details
            </Typography>
          </Box>
        </DialogContent>

        <DialogActions sx={{ px: 3, pb: 3 }}>
          <Button variant="outlined" color="inherit" onClick={qrDialog.onFalse}>
            Close
          </Button>
          <Button
            variant="contained"
            color="primary"
            onClick={() => {
              // TODO: Implement QR code download
              const svg = document.querySelector('svg');
              if (svg) {
                const svgData = new XMLSerializer().serializeToString(svg);
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                const img = new Image();
                img.onload = () => {
                  canvas.width = img.width;
                  canvas.height = img.height;
                  ctx?.drawImage(img, 0, 0);
                  const pngFile = canvas.toDataURL('image/png');
                  const downloadLink = document.createElement('a');
                  downloadLink.download = `station-${station.id}-qr.png`;
                  downloadLink.href = pngFile;
                  downloadLink.click();
                };
                img.src = `data:image/svg+xml;base64,${btoa(svgData)}`;
              }
            }}
          >
            Download QR Code
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}

export default StationEditViewGeneral;
