import type { ChangeEvent } from 'react';

import { useState } from 'react';

import Button from '@mui/material/Button';
import Tooltip from '@mui/material/Tooltip';
import TableRow from '@mui/material/TableRow';
import TableCell from '@mui/material/TableCell';
import IconButton from '@mui/material/IconButton';
import { Box, Link, Switch, Typography, FormControlLabel } from '@mui/material';

import { paths } from 'src/routes/paths';
import { RouterLink } from 'src/routes/components';

import { useBoolean } from 'src/hooks/use-boolean';

// import { updateTestPublishStatus } from 'src/api/staff/tests';

import { Iconify } from 'src/components/iconify';
import { ConfirmDialog } from 'src/components/custom-dialog';
// import ShareDialog from 'src/components/sharing/share-dialog-new';

import type { ICategoryTableData } from 'src/types/categories';

import { toast } from 'src/components/snackbar';

// ----------------------------------------------------------------------

type Props = {
  row: ICategoryTableData;
  onView: () => void;
  onDelete: (id: string) => Promise<void>;
  onUpdateStatus: (id: string, status: boolean) => Promise<void>;
};

export default function CategoriesTableRow({ row, onView, onDelete, onUpdateStatus }: Props) {
  const { id, name, description, total_books, is_active } = row;

  const [activeStatus, setActiveStatus] = useState(is_active);

  const confirm = useBoolean();

  const handleCategoryDeletion = async () => {
    confirm.onFalse();
    try {
      await onDelete(id);
      console.log('Category deleted successfully');
    } catch (error) {
      console.error('Error deleting category:', error);
      toast.error('Failed to delete category. Please try again.');
    }
  };

  const isLiveToggling = useBoolean(false);

  const handleLiveToggle = async (e: ChangeEvent<HTMLInputElement>) => {
    const status = e.currentTarget.checked;
    try {
      isLiveToggling.onTrue();
      await onUpdateStatus(id, status);
      setActiveStatus(status);
    } catch (error) {
      console.error('Error updating category status:', error);
      toast.error('Failed to update category status. Please try again.');
      // Revert the status if update failed
      setActiveStatus(!status);
    } finally {
      isLiveToggling.onFalse();
    }
  };

  const redirectUrl = `${paths.dashboard.books}`;

  return (
    <>
      <TableRow hover>
        <TableCell
          sx={{
            maxWidth: '310px',
          }}
        >
          <Typography
            variant="body2"
            sx={{
              fontWeight: '900',
              lineHeight: '22px',
              whiteSpace: 'nowrap',
              textOverflow: 'ellipsis',
              overflow: 'hidden',
              cursor: 'pointer',
            }}
          >
            <Link color="inherit" underline="hover" component={RouterLink} href={redirectUrl}>
              {name}
            </Link>
          </Typography>

          <Typography
            variant="body2"
            sx={{ fontWeight: '400', lineHeight: '22px', color: 'grey.500' }}
          >
            {total_books} books
          </Typography>
        </TableCell>

        <TableCell
          sx={{
            maxWidth: '640px',
          }}
        >
          <Typography
            variant="body2"
            sx={{
              fontWeight: '400',
              lineHeight: '22px',
              whiteSpace: 'nowrap',
              textOverflow: 'ellipsis',
              overflow: 'hidden',
            }}
          >
            {description}
          </Typography>
        </TableCell>

        <TableCell sx={{ px: 1, whiteSpace: 'nowrap' }}>
          <FormControlLabel
            control={<Switch checked={activeStatus} color="success" onChange={handleLiveToggle} />}
            label="Live"
          />
        </TableCell>

        <TableCell sx={{ px: 1, whiteSpace: 'nowrap' }}>
          <Tooltip title="Edit" placement="top" arrow>
            <IconButton onClick={onView}>
              <Iconify icon="solar:eye-bold" />
            </IconButton>
          </Tooltip>
        </TableCell>

        <TableCell sx={{ px: 1, whiteSpace: 'nowrap' }}>
          <Tooltip title="Delete" placement="top" arrow>
            <IconButton onClick={confirm.onTrue} sx={{ color: 'error.dark' }}>
              <Iconify icon="solar:trash-bin-trash-bold" />
            </IconButton>
          </Tooltip>
        </TableCell>
      </TableRow>

      <ConfirmDialog
        open={confirm.value}
        onClose={confirm.onFalse}
        title={
          <Box display="flex" gap={2} alignItems="center">
            <Iconify icon="solar:trash-bin-trash-bold" /> Delete Category
          </Box>
        }
        content={
          <>
            <Typography variant="subtitle1">Are you sure ?</Typography>
            <Typography variant="body1">
              All Books associated with this category will also be removed
            </Typography>
          </>
        }
        action={
          <Button variant="contained" color="error" onClick={handleCategoryDeletion}>
            Delete
          </Button>
        }
      />

      {/* <ShareDialog
        dialog={shareDialog}
        title={t('test_listing_page.share_test')}
        studentLinkToShare={paths.test.answerTest(id)}
        onShare={shareTestToStudent}
        addtionalPayloadToShareFn={{ testName: name }}
      /> */}
    </>
  );
}
