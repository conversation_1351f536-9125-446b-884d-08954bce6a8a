import { Box, Card, Stack, Skeleton } from '@mui/material';

function BookCardSkeleton() {
  return (
    <Card sx={{ p: 3 }}>
      <Box display="flex" flexDirection="column" gap={2}>
        <Box gap={2} display="flex" flex={1} minWidth={0}>
          <Skeleton variant="circular" width={48} height={48} />

          <Stack sx={{ minWidth: 0, flex: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', minWidth: 0 }}>
              {/* Book title skeleton */}
              <Skeleton
                variant="text"
                width="70%"
                height={24}
                sx={{
                  fontSize: '1.25rem',
                  fontWeight: 'bold',
                }}
              />
            </Box>
            <Box
              display="flex"
              gap="4px"
              alignItems="center"
              sx={{ color: (theme) => theme.palette.grey[500] }}
            >
              {/* Icon skeleton */}
              <Skeleton variant="rectangular" width={16} height={16} />

              {/* Author skeleton */}
              <Skeleton variant="text" width="50%" height={20} sx={{ fontSize: '14px' }} />
            </Box>
          </Stack>
        </Box>

        {/* Tag skeleton */}
        <Skeleton variant="rounded" width={80} height={24} sx={{ borderRadius: '16px' }} />
      </Box>
    </Card>
  );
}

export default BookCardSkeleton;
