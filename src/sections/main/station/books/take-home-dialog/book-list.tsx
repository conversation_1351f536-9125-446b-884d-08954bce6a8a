import type { IBookCard } from 'src/types/books';

import React from 'react';

import { Box, Card, Stack, Avatar, Typography } from '@mui/material';

import { Iconify } from 'src/components/iconify';

import BookEmpty from './book-empty';

// --------------------------------------------------------------------

type Book = Pick<IBookCard, 'name' | 'author' | 'id'>;

type IBookListProp = {
  books: Book[];
  onSelectBookToExchange: (book: Book) => void;
};

const BooksList = ({ books, onSelectBookToExchange }: IBookListProp) => {
  const hasBorrowedBooks = books.length > 0;

  if (!hasBorrowedBooks) return <BookEmpty />;

  return (
    <Stack spacing={2}>
      {books.map((book) => (
        <Card
          key={book.id}
          onClick={() => onSelectBookToExchange(book)}
          sx={{
            display: 'flex',
            alignItems: 'center',
            p: 3,
            gap: 2,
            cursor: 'pointer',
            transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
            border: (theme) => `1px solid ${theme.palette.divider}`,
            '&:hover': {
              transform: 'translateY(-2px)',
              boxShadow: (theme) => theme.shadows[8],
              borderColor: (theme) => theme.palette.primary.main,
            },
          }}
        >
          <Avatar
            alt={book.name}
            sx={{
              width: 48,
              height: 48,
            }}
          >
            <Iconify icon="mdi:book-open-variant-outline" width={24} />
          </Avatar>

          <Stack sx={{ flex: 1, minWidth: 0 }}>
            <Typography
              sx={{
                textTransform: 'capitalize',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                minWidth: 0,
              }}
              fontWeight="bold"
              variant="subtitle1"
            >
              {book.name}
            </Typography>

            <Box display="flex" alignItems="center" gap={0.5} color="text.secondary">
              <Iconify width="16" height="16" icon="mdi:account-outline" />
              <Typography
                textOverflow="ellipsis"
                whiteSpace="nowrap"
                fontSize="14px"
                overflow="hidden"
                variant="caption"
              >
                {book.author}
              </Typography>
            </Box>
          </Stack>

          <Box
            sx={{
              width: 32,
              height: 32,
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              bgcolor: (theme) => theme.palette.action.hover,
            }}
          >
            <Iconify icon="mdi:chevron-right" width={20} color="text.secondary" />
          </Box>
        </Card>
      ))}
    </Stack>
  );
};

export default BooksList;
