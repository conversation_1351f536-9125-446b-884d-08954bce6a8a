import React from 'react';

import { Box, Divider, useTheme, Typography } from '@mui/material';

import { Iconify } from 'src/components/iconify';

const AddNewBookButton = ({ onNewBookFormClick }: { onNewBookFormClick: () => void }) => {
  const theme = useTheme();

  return (
    <Box>
      <Divider sx={{ mx: -2 }} />
      <Box flexShrink={0} pt={2}>
        <Box
          onClick={onNewBookFormClick}
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: 2,
            p: 2.5,
            borderRadius: 2,
            cursor: 'pointer',
            backgroundColor: theme.palette.background.default,
            transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
            border: `2px dashed ${theme.palette.divider}`,
            '&:hover': {
              borderColor: theme.palette.primary.main,
              bgcolor: theme.palette.action.hover,
              transform: 'translateY(-1px)',
            },
          }}
        >
          <Box
            sx={{
              width: 36,
              height: 36,
              borderRadius: '50%',
              bgcolor: theme.palette.primary.lighter || theme.palette.primary.light,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <Iconify icon="mdi:plus" width={20} sx={{ color: theme.palette.primary.main }} />
          </Box>

          <Typography
            sx={{
              fontWeight: 600,
              fontSize: '15px',
              color: theme.palette.text.primary,
            }}
          >
            Add New Book
          </Typography>
        </Box>
      </Box>
    </Box>
  );
};

export default AddNewBookButton;
