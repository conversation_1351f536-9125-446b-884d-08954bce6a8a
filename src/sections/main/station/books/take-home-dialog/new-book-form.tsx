import type { IUseBooleanReturn } from 'src/hooks/use-boolean';

import { z as zod } from 'zod';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';

import { LoadingButton } from '@mui/lab';
import { Box, Stack, Button, Divider, useTheme, Typography } from '@mui/material';

import { useBooksData } from 'src/hooks/useSupabase/useBooks';
import { useCategoryData } from 'src/hooks/useSupabase/useCategories';

import { toast } from 'src/components/snackbar';
import { Scrollbar } from 'src/components/scrollbar';
import { Form, RHFTextField, RHFAutocomplete } from 'src/components/hook-form';

import type { IBookToGive } from '../section/take-home-dialog';

const defaultValues = {
  name: '',
  author: '',
  category_id: '',
  description: '',
};

const NewBookForm = ({
  topArea,
  onCloseNewBookForm,
  chosenBookDetails,
  onCreateExchangeBook,
  confirm,
}: {
  topArea?: React.ReactNode;
  onCloseNewBookForm: () => void;
  chosenBookDetails: IBookToGive | null;
  onCreateExchangeBook: (book: any) => void;
  confirm: IUseBooleanReturn;
}) => {
  const theme = useTheme();

  const { data: categoriesData } = useCategoryData('categories');
  const { booksData } = useBooksData('books');

  const authorData = booksData?.map((item: any) => item.author);

  const NewBookSchema = zod.object({
    name: zod.string().min(1, 'Book name is required'),
    author: zod.string().min(1, 'Author is required'),
    category_id: zod.string().min(1, 'Category is required'),
    description: zod.string().nullable(),
  });

  const methods = useForm({
    resolver: zodResolver(NewBookSchema),
    defaultValues,
  });

  const {
    handleSubmit,
    reset,
    formState: { isSubmitting },
  } = methods;

  useEffect(() => {
    // checking is there id exist, if no, it's new book
    if (chosenBookDetails) reset(chosenBookDetails);
  }, [chosenBookDetails, reset]);

  const onSubmit = handleSubmit(async (data) => {
    try {
      // Find the selected category and station objects
      // Transform form data to match database schema

      // Create new book
      //   await onCreateBook(bookData);
      toast.success('Book created successfully!');
      console.log('Book created successfully');
      console.log('🚀 ~ onSubmit ~ data:', data);

      onCreateExchangeBook(data);
      confirm.onTrue();
    } catch (error) {
      console.error('Error saving book:', error);
      toast.error('Failed to save book. Please try again.');
    }
  });

  return (
    <>
      {/* Header - Fixed at top */}
      {topArea}

      {/* Scrollable Form Content */}
      <Scrollbar sx={{ flex: 1, py: 1, mx: -2, px: 2 }}>
        <Form methods={methods}>
          <Box>
            <Stack spacing={3}>
              <Box>
                <Typography
                  variant="subtitle2"
                  sx={{
                    mb: 1.5,
                    fontWeight: 600,
                    color: theme.palette.text.primary,
                  }}
                >
                  Book Information
                </Typography>
                <Stack spacing={2.5}>
                  <RHFTextField
                    name="name"
                    label="Book Title"
                    placeholder="Enter the book title"
                    fullWidth
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        '&:hover fieldset': {
                          borderColor: theme.palette.primary.main,
                        },
                      },
                    }}
                  />

                  <RHFAutocomplete
                    name="author"
                    label="Author"
                    placeholder="Select or enter author name"
                    helperText="You can select from suggestions or type a new author"
                    freeSolo
                    options={authorData}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        '&:hover fieldset': {
                          borderColor: theme.palette.primary.main,
                        },
                      },
                    }}
                  />
                </Stack>
              </Box>

              <Box>
                <Typography
                  variant="subtitle2"
                  sx={{
                    mb: 1.5,
                    fontWeight: 600,
                    color: theme.palette.text.primary,
                  }}
                >
                  Classification
                </Typography>
                <Stack spacing={2.5}>
                  <RHFAutocomplete
                    name="categoryId"
                    label="Category"
                    placeholder="Select book category"
                    helperText="Choose the most appropriate category"
                    options={categoriesData || []}
                    getOptionLabel={(option) => option.name}
                    isOptionEqualToValue={(option, value) => option.id === value.id}
                    value={
                      categoriesData.find((cat) => cat.id === methods.watch('category_id')) || null
                    }
                    onChange={(_, newValue) => {
                      methods.setValue('category_id', newValue?.id || '', { shouldValidate: true });
                    }}
                    autoComplete
                    disableClearable
                    autoHighlight
                    autoSelect
                  />
                </Stack>
              </Box>

              <Box>
                <Typography
                  variant="subtitle2"
                  sx={{
                    mb: 1.5,
                    fontWeight: 600,
                    color: theme.palette.text.primary,
                  }}
                >
                  Description
                </Typography>
                <RHFTextField
                  name="description"
                  label="Book Description"
                  placeholder="Brief description of the book, its condition, or any notes..."
                  multiline
                  rows={4}
                  fullWidth
                  helperText="This will help others understand what the book is about"
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      '&:hover fieldset': {
                        borderColor: theme.palette.primary.main,
                      },
                    },
                  }}
                />
              </Box>
            </Stack>
          </Box>
        </Form>
      </Scrollbar>

      {/* Fixed Bottom Actions - Outside Form */}
      <Divider sx={{ mx: -2 }} />
      <Box flexShrink={0} mt={2}>
        <Stack
          direction={{ xs: 'column', sm: 'row' }}
          flexWrap="nowrap"
          gap={2}
          justifyContent="center"
        >
          <Button
            onClick={onCloseNewBookForm}
            variant="outlined"
            color="inherit"
            size="large"
            // startIcon={<Iconify icon="mdi:arrow-left" width={18} />}
            sx={{
              fontWeight: 600,
              textWrap: 'nowrap',
              flex: 1,
            }}
          >
            Cancel
          </Button>

          <LoadingButton
            color="primary"
            onClick={onSubmit} // Direct onClick handler
            variant="contained"
            loading={isSubmitting}
            size="large"
            // startIcon={!isSubmitting ? <Iconify icon="mdi:check" width={18} /> : null}
            sx={{
              fontWeight: 600,
              flex: 1,
              textWrap: 'nowrap',
            }}
          >
            {isSubmitting ? 'Creating Book...' : 'Create'}
          </LoadingButton>
        </Stack>
      </Box>
    </>
  );
};

export default NewBookForm;
