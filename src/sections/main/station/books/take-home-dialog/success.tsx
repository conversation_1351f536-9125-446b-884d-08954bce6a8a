import { Box, Typography } from '@mui/material';

import loadingAnimation from 'src/assets/animation/success-animation.json';

import { LottieAnimation } from 'src/components/animate';

const Success = () => (
  <Box
    sx={{
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
      gap: '1.4rem',
      py: 14,
    }}
  >
    <LottieAnimation animationData={loadingAnimation} height={180} width={180} />
    <Typography>Success</Typography>
  </Box>
);

export default Success;
