import type { IUseBooleanReturn } from 'src/hooks/use-boolean';

import React from 'react';

import { Box, Stack, Divider, IconButton, Typography } from '@mui/material';

import { Iconify } from 'src/components/iconify';

const DialogTop = ({ onClose }: { onClose: IUseBooleanReturn['onFalse'] }) => (
  <Box flexShrink={0} display="flex" flexDirection="column" gap={2}>
    <Stack direction="row" justifyContent="space-between" alignItems="center" gap={2}>
      <Typography variant="h6">Take Home</Typography>
      <IconButton aria-label="close dialog" onClick={onClose}>
        <Iconify icon="mingcute:close-line" width={24} />
      </IconButton>
    </Stack>

    <Typography variant="body2" color="text.secondary">
      To take this book home, you must either exchange one of the books you&apos;ve already borrowed
      or add a new book to exchange
    </Typography>

    <Divider sx={{ mx: -2 }} />
  </Box>
);

export default DialogTop;
