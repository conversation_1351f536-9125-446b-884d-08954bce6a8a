import type { BoxProps } from '@mui/material';
import type { IBooksTableData } from 'src/types/books';
import type { TransitionProps } from '@mui/material/transitions';

import { useState, forwardRef } from 'react';

import { Slide, Dialog, useTheme } from '@mui/material';

import { useBoolean, type IUseBooleanReturn } from 'src/hooks/use-boolean';

import { CompactContent } from 'src/layouts/public';

import BooksList from '../take-home-dialog/book-list';
import DialogTop from '../take-home-dialog/dialog-top';
import ConfirmView from '../take-home-dialog/confirm-view';
import NewBookForm from '../take-home-dialog/new-book-form';
import ContentHeader from '../take-home-dialog/content-header';
import AddNewBookButton from '../take-home-dialog/add-new-book-button';
import DialogContentWrapper from '../take-home-dialog/dialog-content-wrapper';

type Props = {
  takeHomeDialogState: IUseBooleanReturn;
  bookToTake: IBooksTableData;
};

const Transition = forwardRef(
  (
    props: TransitionProps & {
      children: React.ReactElement;
    },
    ref: React.Ref<unknown>
  ) => <Slide direction="up" ref={ref} {...props} />
);

const books = [
  { id: '1', name: 'The Midnight Library', author: 'Matt Haig' },
  { id: '2', name: 'Atomic Habits', author: 'James Clear' },
  { id: '3', name: 'The Psychology of Money', author: 'Morgan Housel' },
  { id: '4', name: 'Educated', author: 'Tara Westover' },
  { id: '5', name: 'The Silent Patient', author: 'Alex Michaelides' },
  { id: '6', name: 'Where the Crawdads Sing', author: 'Delia Owens' },
  { id: '7', name: 'The Seven Husbands of Evelyn Hugo', author: 'Taylor Jenkins Reid' },
  { id: '8', name: 'Becoming', author: 'Michelle Obama' },
];

export type IBookToGive = {
  category_id: string;
  description: string;
  name: string;
  author: string;
};

const TakeHomeDialog = ({ takeHomeDialogState, bookToTake }: Props & BoxProps) => {
  const theme = useTheme();

  const confirmSection = useBoolean();
  const newBook = useBoolean();

  const [chosenBookDetails, setChosenBookDetails] = useState<IBookToGive | null>(null);

  const handleChoseOrCreateExchangeBook = (bookDetails: any) => {
    setChosenBookDetails(bookDetails);
    confirmSection.onTrue();
  };

  const handleNewBookFormClick = () => newBook.onTrue();

  const handleCloseNewBookForm = () => newBook.onFalse();

  const handleClose = () => {
    takeHomeDialogState.onFalse();
    setTimeout(() => {
      newBook.onFalse(); // Reset new book form when closing main dialog
      confirmSection.onFalse();
      setChosenBookDetails(null);
    }, 1000);
  };

  // Sample book data - you can replace with your actual data

  return (
    <Dialog
      open={takeHomeDialogState.value}
      fullScreen
      onClose={handleClose}
      aria-labelledby="book-exchange-modal"
      aria-describedby="select-book-to-exchange"
      TransitionComponent={Transition}
      sx={{
        bgcolor: theme.palette.background.default,
      }}
    >
      <CompactContent maxHeight="100dvh" py={2}>
        <DialogTop onClose={handleClose} />

        {chosenBookDetails && confirmSection.value ? (
          // confirm section
          <ConfirmView
            chosenBookDetails={chosenBookDetails}
            bookToTake={bookToTake}
            onClose={handleClose}
            confirm={confirmSection}
          />
        ) : // Book selecting section choose/create
        !newBook.value ? (
          <DialogContentWrapper
            bottomBar={<AddNewBookButton onNewBookFormClick={handleNewBookFormClick} />}
          >
            <BooksList books={books} onSelectBookToExchange={handleChoseOrCreateExchangeBook} />
          </DialogContentWrapper>
        ) : (
          <NewBookForm
            topArea={
              <ContentHeader
                header="Add New Book"
                subtitle=" Fill in the details to add your book to the exchange"
                onClickBackButton={handleCloseNewBookForm}
              />
            }
            confirm={confirmSection}
            onCloseNewBookForm={handleCloseNewBookForm}
            onCreateExchangeBook={handleChoseOrCreateExchangeBook}
            chosenBookDetails={chosenBookDetails}
          />
        )}
      </CompactContent>
    </Dialog>
  );
};

export default TakeHomeDialog;
