import { useState } from 'react';

import { Box, Stack } from '@mui/material';

import { paths } from 'src/routes/paths';

import { useBoolean } from 'src/hooks/use-boolean';
import { useDebounce } from 'src/hooks/use-debounce';

import { MenuButton } from 'src/layouts/components/menu-button';

import FilterSearch from '../filter-search';
import FilterCategories from '../filter-categories';

const useSearchPosts = (gottenQuery: string) => ({
  searchResults: [],
  searchLoading: false,
});

const FilterOptions = () => {
  const [searchQuery, setSearchQuery] = useState('');

  const mobileNavOpen = useBoolean();

  const debouncedQuery = useDebounce(searchQuery);

  const { searchResults, searchLoading } = useSearchPosts(debouncedQuery);

  const handleSearch = (inputValue: string) => {
    setSearchQuery(inputValue);
  };

  return (
    <Box>
      <FilterCategories
        open={mobileNavOpen.value}
        onClose={mobileNavOpen.onFalse}
        data={[
          {
            subheader: 'Categories',
            items: [
              { title: 'horror', path: '?category=horror' },
              { title: 'romance', path: '?category=romance' },
              { title: 'comedy', path: '?category=comedy' },
              { title: 'action', path: '?category=action' },
            ],
          },
        ]}
      />
      <Stack direction="row">
        <MenuButton
          data-slot="menu-button"
          disabled
          onClick={mobileNavOpen.onTrue}
          sx={{ mr: 1, ml: -1 }}
        />
        <FilterSearch
          query={debouncedQuery}
          results={searchResults}
          onSearch={handleSearch}
          loading={searchLoading}
          hrefItem={(title: string) => paths.public.books.detail(title)}
        />
      </Stack>
    </Box>
  );
};

export default FilterOptions;
