import { Box, Stack, Button, Typography } from '@mui/material';

import { Iconify } from 'src/components/iconify'; // Adjust import path as needed

interface BooksListEmptysProps {
  onAddBook?: () => void;
  showAddButton?: boolean;
}

function BooksListEmpty({ onAddBook, showAddButton = true }: BooksListEmptysProps) {
  return (
    <Box
      display="flex"
      flexDirection="column"
      alignItems="center"
      justifyContent="center"
      textAlign="center"
      py={8}
      px={3}
    >
      <Stack spacing={3} alignItems="center" maxWidth={400}>
        {/* Icon */}
        <Box
          sx={{
            width: 120,
            height: 120,
            borderRadius: '50%',
            backgroundColor: (theme) => theme.palette.grey[100],
            color: (theme) => theme.palette.text.secondary,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            mb: 1,
          }}
        >
          <Iconify icon="mdi:book-open-variant-outline" width={60} />
        </Box>

        {/* Title */}
        <Typography
          variant="h5"
          fontWeight="600"
          sx={{ color: (theme) => theme.palette.text.primary }}
        >
          No Books Found
        </Typography>

        {/* Subtitle */}
        <Typography
          variant="body1"
          sx={{
            color: (theme) => theme.palette.text.secondary,
            lineHeight: 1.6,
          }}
        >
          Support your local bookstores and building a community of readers.
        </Typography>

        {/* Add Book Button */}
        {showAddButton && (
          <Button
            variant="contained"
            startIcon={<Iconify icon="mdi:plus" width={20} />}
            onClick={onAddBook}
            sx={{
              mt: 2,
              px: 3,
              py: 1.5,
              borderRadius: 2,
              textTransform: 'none',
              fontSize: '16px',
              fontWeight: '600',
            }}
          >
            Add Your First Book
          </Button>
        )}
      </Stack>
    </Box>
  );
}

export default BooksListEmpty;
