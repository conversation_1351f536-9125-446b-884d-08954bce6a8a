import type { BoxProps } from '@mui/material';
import type { IBooksTableData } from 'src/types/books';

import Avatar from '@mui/material/Avatar';
import { Box, Card, Chip, Link, Stack, Button, Typography } from '@mui/material';

import { paths } from 'src/routes/paths';
import { RouterLink } from 'src/routes/components';

import { Iconify } from 'src/components/iconify';

type ItemProps = BoxProps & {
  item: IBooksTableData;
  onClickReturn?: () => void;
};

function BookCard({ item, sx, onClickReturn }: ItemProps) {
  const redirectUrl = paths.public.books.detail(item.id);

  return (
    <Link component={RouterLink} href={redirectUrl} underline="none">
      <Card sx={{ p: 3, cursor: 'pointer', ...sx }}>
        <Box display="flex" flexDirection="column" gap={2}>
          <Box gap={2} display="flex" flex={1} minWidth={0}>
            <Avatar alt={item.name} sx={{ width: 48, height: 48 }}>
              <Iconify icon="mdi:book-open-variant-outline" width={24} />
            </Avatar>
            <Stack sx={{ minWidth: 0 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', minWidth: 0 }}>
                <Typography
                  sx={{
                    textTransform: 'capitalize',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    minWidth: 0,
                  }}
                  fontWeight="bold"
                  variant="subtitle1"
                >
                  {item.name}
                </Typography>
              </Box>
              <Box
                display="flex"
                alignItems="center"
                gap={0.5}
                sx={{ color: (theme) => theme.palette.grey[500] }}
              >
                <Iconify width="16" height="16" icon="mdi:account" />
                <Typography
                  sx={{
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                  }}
                  variant="body1"
                  fontSize="14px"
                >
                  {item.author}
                </Typography>
              </Box>
            </Stack>
          </Box>

          {/* Tags */}
          <Box display="flex" alignItems="center" justifyContent="space-between">
            <Chip
              label={item.category?.name}
              size="small"
              variant="outlined"
              sx={{
                width: 'min-content',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
              }}
            />

            {/* Return Button (placeholder) */}
            {onClickReturn && (
              <Button
                onClick={(e) => {
                  e.preventDefault();
                  onClickReturn();
                }}
                variant="soft"
                color="success"
                sx={{ mt: 1 }}
              >
                Return
              </Button>
            )}
          </Box>
        </Box>
      </Card>
    </Link>
  );
}

export default BookCard;
