import Avatar from '@mui/material/Avatar';
import Button from '@mui/material/Button';
import { Box, Chip, Stack, IconButton, Typography } from '@mui/material';

import { useParams, useRouter } from 'src/routes/hooks';

import { useBoolean } from 'src/hooks/use-boolean';
import { useBooksData } from 'src/hooks/useSupabase/useBooks';

import { toast } from 'src/components/snackbar';
import { Iconify } from 'src/components/iconify';

import { DonateBy } from 'src/sections/main/station/books/single-book/donated-by';

import { useAuthContext } from 'src/auth/hooks';

import TakeHomeDialog from '../section/take-home-dialog';

const SingleBook = () => {
  const takeHomeDialog = useBoolean();
  const isAvailableToRead = useBoolean(true);

  const { bookId, loading } = useParams();
  const { authenticated, signInDialogStatus } = useAuthContext();

  const { booksData, updateBook } = useBooksData('books');

  const filterBookData = booksData.filter((item: any) => item.id === Number(bookId));

  const navigate = useRouter();

  // TODO: replace with actual book
  const bookDetails = {
    id: '1',
    name: '1984',
    author: 'George Orwell',
    donated_by: 'John Doe',
  };

  const handleReadNow = async (id: string, read_book: string) => {
    if (!authenticated) {
      signInDialogStatus.onTrue();
      return;
    }
    const promise = new Promise((resolve) => setTimeout(resolve, 3000));
    try {
      await updateBook(id, { borrower_method: 'read_now' });
      toast.promise(promise, {
        loading: 'Reserving book...',
        success: () => `Book reserved!`,
        error: 'Error',
        closeButton: false,
      });

      await promise;
      // TODO: REDIRECT THE USER TO THE MY BOOKS PAGE
      isAvailableToRead.onFalse();
    } catch (error) {
      console.error(error);
    }
  };

  // Show loading state if data is still loading
  if (loading) {
    return <Typography>Loading...</Typography>;
  }

  // Show message if no books data
  if (!booksData || booksData.length === 0) {
    return <Typography>No books found.</Typography>;
  }

  return (
    <>
      {filterBookData?.map((item) => (
        <Stack
          key={item.id}
          gap={2}
          minHeight={{
            xs: 'calc(100svh - var(--layout-header-mobile-height) - 16px)',
            md: 'calc(100svh - var(--layout-header-desktop-height) - 16px)',
          }}
        >
          <IconButton
            aria-label="go back"
            sx={{
              alignSelf: 'start',
            }}
            onClick={() => navigate.back()}
          >
            <Iconify icon="mdi:arrow-left" />
          </IconButton>
          <Box gap={2} display="flex" alignItems="center" minWidth={0}>
            <Avatar alt="Book" sx={{ width: 48, height: 48 }}>
              <Iconify icon="mdi:book-open-variant-outline" width={24} />
            </Avatar>
            <Stack sx={{ minWidth: 0 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', minWidth: 0 }}>
                <Typography
                  sx={{
                    textTransform: 'capitalize',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    minWidth: 0,
                  }}
                  fontWeight="bold"
                  variant="h5"
                >
                  {item.name}
                </Typography>
              </Box>
              <Box
                display="flex"
                gap="4px"
                alignItems="center"
                sx={{ color: (theme) => theme.palette.grey[500] }}
              >
                <Iconify width="16" height="16" icon="mdi:account" />
                <Typography
                  sx={{
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                  }}
                  variant="body1"
                  fontSize="14px"
                >
                  {item.author}
                </Typography>
              </Box>
            </Stack>
          </Box>
          <Chip
            label={item.category?.name || 'Uncategorized'}
            size="small"
            variant="outlined"
            sx={{
              width: 'min-content',
              overflow: 'hidden',
              whiteSpace: 'nowrap',
            }}
          />
          <Typography variant="caption" sx={{ fontSize: '16px', textAlign: 'start' }}>
            {item.description}
          </Typography>
          <Box gap={2} display="flex" flex={1} sx={{ mt: 2 }}>
            <Button
              onClick={() => handleReadNow(item.id, 'read_book')}
              disabled={!isAvailableToRead.value}
              fullWidth
              variant="soft"
              size="large"
              color="warning"
            >
              Read Now
            </Button>
            <Button
              onClick={() =>
                !authenticated ? signInDialogStatus.onTrue() : takeHomeDialog.onTrue()
              }
              fullWidth
              variant="soft"
              size="large"
              color="secondary"
            >
              Take Home
            </Button>
          </Box>
          {bookDetails.donated_by && <DonateBy name={bookDetails.donated_by} />}
          <TakeHomeDialog takeHomeDialogState={takeHomeDialog} bookToTake={item} />
        </Stack>
      ))}
    </>
  );
};

export default SingleBook;
