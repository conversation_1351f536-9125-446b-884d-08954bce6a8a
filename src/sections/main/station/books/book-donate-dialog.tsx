import { Dialog, Typography, DialogContent } from '@mui/material';

import { useBoolean, type IUseBooleanReturn } from 'src/hooks/use-boolean';

import NewBookForm from './take-home-dialog/new-book-form';

// ----------------------------------------------------------------------

type INewBookDialogProps = {
  dialogControl: IUseBooleanReturn;
};

const BookDonateDialog = ({ dialogControl }: INewBookDialogProps) => {
  const confirm = useBoolean();

  const handleDonateBook = (data: any) => {
    console.log('🚀 ~ handleDonateBook ~ data:', data);
  };

  return (
    <Dialog open={dialogControl.value} fullWidth maxWidth="xs">
      <DialogContent
        sx={{
          py: 2,
          height: 1,
        }}
      >
        <NewBookForm
          topArea={<Typography variant="h5">Donate Book</Typography>}
          chosenBookDetails={null}
          onCloseNewBookForm={dialogControl.onFalse}
          onCreateExchangeBook={(data) => handleDonateBook(data)}
          confirm={confirm}
        />
      </DialogContent>
    </Dialog>
  );
};

export default BookDonateDialog;
