import type { ICategoryTableData, ICategoryTableFilters } from 'src/types/categories';

export const CATEGORY_TABLE_COLUMNS = [
  { id: 'name', label: 'Name', width: 310, align: 'left' },
  { id: 'description', label: 'Description', width: 640, align: 'left' },
  { id: 'status', label: 'Status', width: 50, align: 'left' },
  { id: 'edit', label: '', width: 50, align: 'right' },
  { id: 'delete', label: '', width: 50, align: 'right' },
];

export const DEFAULT_CATEGORY_TABLE_FILTERS: ICategoryTableFilters = {
  searchKeyword: '',
  isActive: undefined,
};

export const categoryTableData: ICategoryTableData[] = [
  {
    id: '1',
    name: 'wwwwwwwwwwwwwwwwwwwwwwwwwwwwwwww',
    total_books: 10,
    color: '#E00034',
    description:
      'Description 1 ips Description 1 ips Description 1 ips Description 1 ips Description 1 ips Description 1 ips Description 1 ips Description 1 ips Description 1 ips',
    is_active: true,
  },
  {
    id: '2',
    name: 'Category 2',
    total_books: 20,
    color: '#FFB000',
    description: 'Description 2',
    is_active: false,
  },
  {
    id: '3',
    name: 'Category 3',
    total_books: 30,
    color: '#00B2FF',
    description: 'Description 3',
    is_active: true,
  },
];
