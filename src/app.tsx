import 'src/global.css';

import { Router } from 'src/routes/sections';

import { useScrollToTop } from 'src/hooks/use-scroll-to-top';

import { ThemeProvider } from 'src/theme/theme-provider';

import { Snackbar } from 'src/components/snackbar';
import { ProgressBar } from 'src/components/progress-bar';
import { MotionLazy } from 'src/components/animate/motion-lazy';
import { SettingsDrawer, defaultSettings, SettingsProvider } from 'src/components/settings';

import { StationProvider } from './station/context';
import { AuthProvider } from './auth/context/supabase';
import { SignInDialog } from './layouts/components/signin-dialog';
import { ScanStationDialog } from './layouts/components/scan-station-dialog';

export default function App() {
  useScrollToTop();

  return (
    <AuthProvider>
      <StationProvider>
        <SettingsProvider settings={defaultSettings}>
          <ThemeProvider>
            <MotionLazy>
              <Snackbar />
              <ProgressBar />
              <SignInDialog />
              <ScanStationDialog />
              <SettingsDrawer />
              <Router />
            </MotionLazy>
          </ThemeProvider>
        </SettingsProvider>
      </StationProvider>
    </AuthProvider>
  );
}
