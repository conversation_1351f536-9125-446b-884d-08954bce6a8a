import type { IData } from './common';

export interface IStationsItem {
  id: string;
  name: string;
  location?: string;
  is_active: boolean;
  total_book_count?: number;
  created_at?: string;
  updated_at?: string;
}

// NEW_CHANGES

// using at create station
export type IStationData = {
  id: string;
  name: string;
  location: string;
  isActive: boolean;
};

export type IStationWithDate = IStationData & IData;

export type IStationWithBookCount = IStationWithDate & {
  books: [{ count: number }];
};

export type IStation = Omit<IStationWithBookCount, 'books'> & {
  bookCount: number;
};
