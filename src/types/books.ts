// create some dummy data for the books

import { type IDateValue } from './common';

export type IBookTableFilter = {
  categoryId?: string;
  status?: IBookStatus;
  searchQuery?: string;
};

export type IBookTableFilterValue =
  | IBookTableFilter['categoryId']
  | IBookTableFilter['status']
  | IBookTableFilter['searchQuery'];

export type ITestTableItem = {
  id: string;
  name: string;
  instruction: string;
  totalQuestions: number;
  testDuration: number;
  isPublished: boolean;
  isRandomized: boolean;
  upperThreshold: number | null;
  lowerThreshold: number | null;
  nextTestId: string | null;
  prevTestId: string | null;
  isPrimary: boolean;
  totalResults: number;
  category: {
    id: string;
    name: string;
    color: string;
    isPublished: boolean;
  } | null;
  levels: ITestLevel[] | null;
};

export type IBooksTableData = {
  id: string;
  name: string;
  author: string;
  borrower: {
    id: string;
    email: string;
  };
  category: {
    id: string;
    name: string;
    color: string;
    description: string;
  };
  donator?: {
    id: string;
    email: string;
    user_name: string;
  } | null;
  station_id: string;
  description: string;
  created_at: string;
  is_active: boolean;
  status: string;
  borrower_method?: string;
};

export type IBookRequestTableData = {
  id: string;
  name: string;
  author: string;
  category: {
    id: string;
    name: string;
    color: string;
    isPublished: boolean;
  } | null;
  station: {
    id: string;
    name: string;
    location: string;
  } | null;
  description: string;
  requestedBy: {
    id: string;
    name: string;
    email: string;
  };
  requestedAt: string;
  status: 'pending' | 'approved' | 'rejected';
};

export type IBooksTableFilters = {
  searchKeyword: string;
  categoryId?: string;
  is_available?: boolean;
  is_active?: boolean;
};

export type IBookRequestTableFilters = {
  searchKeyword: string;
  status?: 'pending' | 'approved' | 'rejected';
  categoryId?: string;
};

export type IBookCard = {
  id: string;
  name: string;
  author: string;
  tags: string[];
  description: string;
  postedAt: IDateValue;
};

// create some mock data for book requests
export const bookRequestsTableData: IBookRequestTableData[] = [
  {
    id: '1',
    name: 'The Great Gatsby',
    author: 'F. Scott Fitzgerald',
    category: { id: '1', name: 'Fiction', color: '#E00034', isPublished: true },
    station: { id: '1', name: 'Fiction Shelf A', location: 'Floor 1, Section A' },
    description: 'A classic American novel set in the Jazz Age.',
    requestedBy: { id: '1', name: 'John Doe', email: '<EMAIL>' },
    requestedAt: '2024-01-15T10:30:00Z',
    status: 'pending',
  },
  {
    id: '2',
    name: 'Atomic Habits',
    author: 'James Clear',
    category: { id: '3', name: 'Self-Help', color: '#00B2FF', isPublished: true },
    station: { id: '3', name: 'Self-Help Shelf C', location: 'Floor 2, Section A' },
    description: 'An easy and proven way to build good habits and break bad ones.',
    requestedBy: { id: '2', name: 'Jane Smith', email: '<EMAIL>' },
    requestedAt: '2024-01-14T14:20:00Z',
    status: 'approved',
  },
];

export interface ITestLevel {
  id: string;
  name: string;
  minPercentage: number;
}

export interface ICreateOrUpdateTest {
  id: string;
  name: string;
  instruction: string;
  isRandomized: boolean;
  dimension: number;
  totalQuestions: number;
  testDuration: number;
  isPublished: boolean;
  isArchived: boolean;
  levels: Partial<ITestLevel>[];
}

type IUserPremise = {
  premise: {
    name: string;
  };
};

type IUser = {
  id: string;
  name: string;
  surname: string;
  email: string;
  premises: IUserPremise[];
};

type ITestResult = {
  id: string;
  createdAt: Date;
  studentPoints: number;
  totalPoints: number;
  student: IUser;
  testLevels: ITestLevel;
  nextTestId: string;
};

export type ITestSection = {
  id: string;
  name: string;
  instruction: string;
  position: number;
  _tempId?: string;
};

export type ITestQuestion = {
  id: string;
  _tempId?: string;
  questionId: string;
  sectionId: string;
  testId: string;
  question: string;
  answers: Array<{ text: string; points: number }>;
  totalPoints: number;
  instruction?: string;
  imagePath?: string;
  audioPath?: string;
  videoPath?: string;
  mimeType?: string;
  isExternal: boolean;
  questionType: 'text' | 'single' | 'multiple';
  tags: string[];
  position: number;
  isUpdatedOnQuestionsTable: boolean;
  isPublished: boolean;
  isArchived: boolean;
  createdAt: string;
  updatedAt: string;
  isNewQuestion?: boolean;
  questionNumber: number;
};

type ITestCreatedBy = {
  name: string;
  surname: string;
};

export type ITestDetailsResponse = {
  id: string;
  name: string;
  instruction: string;
  isRandomized: boolean;
  dimension?: number;
  createdBy: ITestCreatedBy;
  categoryId: string;
  totalQuestions: number;
  totalResults: number;
  testDuration: number;
  upperThreshold: number | null;
  lowerThreshold: number | null;
  nextTestId: string | null;
  prevTestId: string | null;
  isPrimary: boolean;
  isPublished: boolean;
  isArchived: boolean;
  isRepeatable: boolean;
  createdAt: string;
  updatedAt: string;
  levels: ITestLevel[];
  results: ITestResult[];
  testQuestions: ITestQuestion[];
  sections: ITestSection[];
  category: {
    isPublished: boolean;
    name: string;
  };
};

// const newQuestionYupSchema = Yup.object({
//   question: Yup.string().required('Question cannot be empty').min(1),
//   answers: Yup.array().of(
//     Yup.object({
//       text: Yup.string().required('Answer text cannot be empty').min(1),
//       points: Yup.number().required(),
//     })
//   ),
//   totalPoints: Yup.number().integer().positive('Total points must be a non-negative integer'),
//   instruction: Yup.string().nullable(),
//   imagePath: Yup.string().nullable(),
//   audioPath: Yup.string().nullable(),
//   videoPath: Yup.string().nullable(),
//   mimeType: Yup.string().nullable(),
//   questionType: Yup.string().oneOf(['single', 'multiple', 'text']).required(),
//   tags: Yup.array().of(Yup.string()).default([]),
//   isPublished: Yup.boolean().default(true),
// });

// const saveTestQuestionsYupSchema = Yup.array().of(
//   newQuestionYupSchema.shape({
//     id: Yup.string().uuid(),
//     sectionId: Yup.string().uuid().nullable(),
//     testId: Yup.string().uuid().required(),
//     position: Yup.number().required(),
//     isUpdatedOnQuestionsTable: Yup.boolean().default(false),
//     questionId: Yup.string().uuid().nullable(),
//     _tempId: Yup.string().uuid().optional(),
//   })
// );

// const saveTestSectionsYupSchema = Yup.object({
//   id: Yup.string().uuid(),
//   name: Yup.string().required(),
//   instruction: Yup.string().default(''),
//   position: Yup.number().required(),
//   testId: Yup.string().uuid().required(),
//   questions: saveTestQuestionsYupSchema.default([]),
//   _tempId: Yup.string().uuid().optional(),
// });

// export const saveTestYupSchema = Yup.object({
//   independentQuestions: saveTestQuestionsYupSchema.default([]),
//   sections: Yup.array().of(saveTestSectionsYupSchema.default([])),
//   isRandomized: Yup.boolean().default(false),
//   dimension: Yup.number().nullable().default(null),
//   totalQuestions: Yup.number().default(0),
// });

// export type SaveTestSectionsYupSchema = Yup.InferType<typeof saveTestSectionsYupSchema>;

// export type SaveTestQuestionsYupSchema = Yup.InferType<typeof saveTestYupSchema>;

// export type ICreateOrUpdateTestData = Partial<ICreateOrUpdateTest>;

// NEW

export type IBooksData = {
  id: string;
  name: string;
  author: string;
  category: {
    id: string;
    name: string;
    color: string;
  };
  borrowerId: string;
  isActive: boolean;
  status: string;
};

export type IBook = Omit<IBooksData, 'borrowerId'> & {
  isAvailable: boolean;
};

export enum IBookStatus {
  APPROVED = 'approved',
  PENDING = 'pending',
  REJECTED = 'rejected',
}

export type IBooksTableFiltersNew = {
  name: string;
  categories: { id: string; name: string }[];
  status: 'all' | IBookStatus;
  isActive?: boolean;
};

export const DEFAULT_BOOKS_TABLE_FILTERS_NEW: IBooksTableFiltersNew = {
  name: '',
  categories: [],
  status: 'all',
  isActive: undefined,
};

export const BOOK_STATUS_OPTIONS_NEW = [
  { value: IBookStatus.APPROVED, label: 'Approved' },
  { value: IBookStatus.PENDING, label: 'Pending' },
  { value: IBookStatus.REJECTED, label: 'Rejected' },
];

export type IBookItemNew = {
  id: string;
  name: string;
  author: string;
  category: {
    id: string;
    name: string;
    color: string;
  };
  isAvailable: boolean;
  isActive: boolean;
  status: IBookStatus;
};

export const _categories = [
  { id: 'cat_1', name: 'Fiction', color: '#3498db' },
  { id: 'cat_2', name: 'Non-Fiction', color: '#2ecc71' },
  { id: 'cat_3', name: 'Fantasy', color: '#9b59b6' },
  { id: 'cat_4', name: 'Science', color: '#f39c12' },
  { id: 'cat_5', name: 'History', color: '#e74c3c' },
  { id: 'cat_6', name: 'Biography', color: '#1abc9c' },
  { id: 'cat_7', name: 'Poetry', color: '#e67e22' },
  { id: 'cat_8', name: 'Mystery', color: '#34495e' },
  { id: 'cat_9', name: 'Horror', color: '#c0392b' },
  { id: 'cat_10', name: 'Self-Help', color: '#16a085' },
];

export const _bookList: IBookItemNew[] = Array.from({ length: 55 }, (_, index) => {
  const randomCategory = _categories[Math.floor(Math.random() * _categories.length)];

  return {
    id: `book_${index + 1}`,
    name: `Book Title ${index + 1}`,
    author: `Author ${String.fromCharCode(65 + (index % 26))}`,
    category: randomCategory,
    isAvailable: Math.random() > 0.3,
    isActive: Math.random() > 0.1,
    status: [IBookStatus.APPROVED, IBookStatus.PENDING, IBookStatus.REJECTED][index % 3],
  };
});
