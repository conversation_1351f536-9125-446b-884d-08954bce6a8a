import { z } from 'zod';

export const StationEditCreateSchema = z.object({
  name: z
    .string()
    .min(1, 'Station name is required')
    .max(100, 'Station name must be less than 100 characters')
    .trim(),
  location: z
    .string()
    .min(1, 'Station location is required')
    .max(200, 'Station location must be less than 200 characters')
    .trim(),
  isActive: z.boolean().default(true),
});

export type IStationEditCreateSchema = z.infer<typeof StationEditCreateSchema>;
