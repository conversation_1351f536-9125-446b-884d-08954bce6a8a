import { Dialog, IconButton, Typography, DialogTitle, DialogContent } from '@mui/material';

import { useStationContext } from 'src/station/hooks';

import { Iconify } from 'src/components/iconify';

// ----------------------------------------------------------------------

export function ScanStationDialog() {
  const { scanStationDialogStatus } = useStationContext();

  return (
    <Dialog
      fullWidth
      maxWidth="xs"
      open={scanStationDialogStatus.value}
      onClose={scanStationDialogStatus.onFalse}
    >
      <DialogTitle sx={{ pb: 2 }}>Station Not Found</DialogTitle>

      <IconButton
        aria-label="close"
        onClick={scanStationDialogStatus.onFalse}
        sx={{
          position: 'absolute',
          right: 8,
          top: 20,
          color: (theme) => theme.palette.grey[500],
        }}
      >
        <Iconify icon="mingcute:close-line" />
      </IconButton>

      <DialogContent sx={{ typography: 'body2' }}>
        <Typography sx={{ mb: 2 }}>Please scan the station QR code to continue.</Typography>
      </DialogContent>
    </Dialog>
  );
}
