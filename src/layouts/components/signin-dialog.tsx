import Button from '@mui/material/Button';
import { Dialog, IconButton, DialogTitle, DialogActions, DialogContent } from '@mui/material';

import { Iconify } from 'src/components/iconify';

import { useAuthContext } from 'src/auth/hooks';
import { signInWithGoogle } from 'src/auth/context/supabase';

// ----------------------------------------------------------------------

export function SignInDialog() {
  const { signInDialogStatus } = useAuthContext();
  // Google sign-in
  const handleGoogleSignIn = () => {
    signInWithGoogle();
  };

  return (
    <Dialog
      fullWidth
      maxWidth="xs"
      open={signInDialogStatus.value}
      onClose={signInDialogStatus.onFalse}
    >
      <DialogTitle sx={{ pb: 2 }}>Sign In to ReadBox</DialogTitle>

      <IconButton
        aria-label="close"
        onClick={signInDialogStatus.onFalse}
        sx={{
          position: 'absolute',
          right: 8,
          top: 20,
          color: (theme) => theme.palette.grey[500],
        }}
      >
        <Iconify icon="mingcute:close-line" />
      </IconButton>

      <DialogContent sx={{ typography: 'body2' }} />

      <DialogActions>
        <Button
          fullWidth
          color="inherit"
          size="large"
          type="button"
          variant="contained"
          onClick={handleGoogleSignIn}
          sx={{ mb: 2 }}
          startIcon={
            <img
              src="https://developers.google.com/identity/images/g-logo.png"
              alt="Google"
              style={{ width: 20, height: 20 }}
            />
          }
        >
          Sign in with Google
        </Button>
      </DialogActions>
    </Dialog>
  );
}
