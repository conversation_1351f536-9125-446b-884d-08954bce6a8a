import type { ButtonProps } from '@mui/material/Button';
import type { Theme, SxProps } from '@mui/material/styles';

import { useCallback } from 'react';

import Button from '@mui/material/Button';

import { signOut } from 'src/auth/context/supabase';

// ----------------------------------------------------------------------

type Props = ButtonProps & {
  sx?: SxProps<Theme>;
  onClose?: () => void;
};

export function SignOutButton({ onClose, ...other }: Props) {
  const handleLogout = useCallback(async () => {
    try {
      await signOut();

      // Close any modals/dialogs first
      onClose?.();

      // The Event handler notified by AuthProvider will handle the auth state change automatically
    } catch (error) {
      console.error('Logout error:', error);
    }
  }, [onClose]);

  return (
    <Button fullWidth variant="soft" size="large" color="error" onClick={handleLogout} {...other}>
      Logout
    </Button>
  );
}
