import { Navigate, useRoutes } from 'react-router-dom';

import { RoleBasedPageRedirect } from 'src/routes/components/role-based-routing';

import { CONFIG } from 'src/config-global';

import { authRoutes } from './auth';
import { mainRoutes } from './main';
import { errorRoutes } from './error';
import { dashboardRoutes } from './dashboard';

// ----------------------------------------------------------------------

export function Router() {
  return useRoutes([
    {
      path: CONFIG.auth.redirectPath,
      element: <RoleBasedPageRedirect />,
    },

    // Auth
    ...authRoutes,

    // Dashboard
    ...dashboardRoutes,

    // Main
    ...mainRoutes,

    // Error
    ...errorRoutes,

    // No match
    { path: '*', element: <Navigate to="/404" replace /> },
  ]);
}
