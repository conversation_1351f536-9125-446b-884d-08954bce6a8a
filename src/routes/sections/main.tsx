import { lazy, Suspense } from 'react';
import { Outlet } from 'react-router-dom';

import { CONFIG } from 'src/config-global';
import LandingPage from 'src/pages/main/home';
import { StationGuard } from 'src/station/guard';
import { PublicLayout } from 'src/layouts/public';
import Station from 'src/pages/main/station/station';

import { LoadingScreen } from 'src/components/loading-screen';

import SingleBook from 'src/sections/main/station/books/single-book/single-book';

import { AuthGuard } from 'src/auth/guard';

import { paths } from '../paths';
import { usePathname } from '../hooks';

// Main Pages
const ShelfBooksPage = lazy(() => import('src/pages/main/station/books'));
const MyBooksPage = lazy(() => import('src/pages/main/my-books'));

// ----------------------------------------------------------------------

const LayoutContent = () => {
  const pathname = usePathname();
  const isLandingPage = pathname === paths.public.root;

  return (
    <Suspense fallback={<LoadingScreen />}>
      <PublicLayout content={{ compact: !isLandingPage }}>
        <Outlet />
      </PublicLayout>
    </Suspense>
  );
};

export const mainRoutes = [
  {
    path: '/',
    element: <LayoutContent />,
    children: [
      {
        element: <LandingPage />,
        index: true,
      },
      {
        // it's a page which used to setting the station details to the local storage, and we will rediect the user to the book listing page or landing page based on the visit status.
        path: 'station/:stationId',
        element: <Station />,
      },
      {
        element: (
          <StationGuard>
            <Outlet />
          </StationGuard>
        ),
        children: [
          {
            path: 'station',
            element: <ShelfBooksPage />,
          },
          {
            path: 'book/:bookId',
            element: <SingleBook />,
          },
        ],
      },
      {
        element: CONFIG.auth.skip ? (
          <Outlet />
        ) : (
          <AuthGuard>
            <Outlet />
          </AuthGuard>
        ),
        children: [
          {
            path: 'my-books',
            element: <MyBooksPage />,
          },
        ],
      },
    ],
  },
];
