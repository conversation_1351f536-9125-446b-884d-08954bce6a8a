import { lazy, Suspense } from 'react';
import { Outlet } from 'react-router-dom';

import { CONFIG } from 'src/config-global';
import { UserRoles } from 'src/constants/system';
import { DashboardLayout } from 'src/layouts/dashboard';

import { LoadingScreen } from 'src/components/loading-screen';

import { AuthGuard } from 'src/auth/guard';

// ----------------------------------------------------------------------

const StationList = lazy(() => import('src/pages/dashboard/station/list'));
const StationDetails = lazy(() => import('src/pages/dashboard/station/details'));
const BooksManagementPage = lazy(() => import('src/pages/dashboard/book/list'));
const CategoriesManagementPage = lazy(() => import('src/pages/dashboard/category/list'));

// ----------------------------------------------------------------------

const layoutContent = (
  <DashboardLayout>
    <Suspense fallback={<LoadingScreen />}>
      <Outlet />
    </Suspense>
  </DashboardLayout>
);

export const dashboardRoutes = [
  {
    path: 'dashboard',
    element: CONFIG.auth.skip ? (
      <>{layoutContent}</>
    ) : (
      <AuthGuard allowedRoles={[UserRoles.ADMIN]}>{layoutContent}</AuthGuard>
    ),
    children: [
      {
        path: 'stations',
        children: [
          { element: <StationList />, index: true },
          {
            path: ':stationId',
            element: <StationDetails />,
          },
        ],
      },
      { path: 'books', element: <BooksManagementPage /> },
      { path: 'categories', element: <CategoriesManagementPage /> },
      // { path: 'account-settings', element: <AdminAccountSettingsPage /> },
    ],
  },
];
