const ROOTS = {
  PUBLIC: '/',
  AUTH: '/auth',
  DASHBOARD: '/dashboard',
  REDIRECT: '/redirect',
};

export const paths = {
  faqs: '/faqs',

  // AUTH
  auth: {
    signIn: `${ROOTS.AUTH}/sign-in`,
  },

  // DASHBOARD
  dashboard: {
    stations: {
      root: `${ROOTS.DASHBOARD}/stations`,
      detail: (stationId: string) => `${ROOTS.DASHBOARD}/stations/${stationId}`,
    },
    books: `${ROOTS.DASHBOARD}/books`,
    categories: `${ROOTS.DASHBOARD}/categories`,
    // users: `${ROOTS.DASHBOARD}/users`,
    // accountSettings: `${ROOTS.DASHBOARD}/account-settings`,
  },

  // USER
  public: {
    root: ROOTS.PUBLIC,
    station: {
      books: (shelfId: string) => `/station/${shelfId}`,
      root: '/station',
    },
    books: {
      detail: (bookId: string) => `/book/${bookId}`,
    },
  },

  redirect: ROOTS.REDIRECT,

  page403: '/403',
  page404: '/404',
  page500: '/500',
};
