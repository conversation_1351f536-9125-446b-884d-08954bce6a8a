import { useMemo, useCallback } from 'react';

import { useBoolean } from 'src/hooks/use-boolean';
import { useLocalStorage } from 'src/hooks/use-local-storage';

import { StationContext } from 'src/station/context/station-context';
import { STATION_TTL, STATION_STORAGE_KEY } from 'src/constants/stations';

import type { StoredStation, StationContextValue } from '../types';

type Props = {
  children: React.ReactNode;
};

// ----------------------------------------------------------------------

const DEFAULT_STATION_STATE: StoredStation = {
  stationId: null,
  stationName: null,
  stationLocation: null,
  timestamp: null,
};

export function StationProvider({ children }: Props) {
  const {
    state: station,
    setState: setStation,
    resetState: resetStation,
  } = useLocalStorage<StoredStation>(STATION_STORAGE_KEY, DEFAULT_STATION_STATE);

  const scanStationDialogStatus = useBoolean();

  const isExpired = useCallback(() => {
    if (!station.timestamp) return true;
    return Date.now() - station.timestamp > STATION_TTL; // 3 hours
  }, [station.timestamp]);

  const value = useMemo<StationContextValue>(
    () => ({
      station,
      setStation,
      resetStation,
      isExpired,
      scanStationDialogStatus,
    }),
    [station, setStation, resetStation, scanStationDialogStatus, isExpired]
  );

  return <StationContext.Provider value={value}>{children}</StationContext.Provider>;
}
