import type { IUseBooleanReturn } from 'src/hooks/use-boolean';

export type StoredStation = {
  stationId: string | null;
  stationName: string | null;
  stationLocation: string | null;
  timestamp: number | null;
};

export type StationContextValue = {
  station: StoredStation;
  setStation: (update: StoredStation | Partial<StoredStation>) => void;
  resetStation: () => void;
  isExpired: () => boolean;
  scanStationDialogStatus: IUseBooleanReturn;
};
