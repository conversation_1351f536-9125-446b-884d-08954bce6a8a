import { m } from 'framer-motion';

import { Container, Typography } from '@mui/material';

import { ForbiddenIllustration } from 'src/assets/illustrations';

import { varBounce, MotionContainer } from 'src/components/animate';

import { useStationContext } from '../hooks';

type Props = {
  sx?: any;
  children: React.ReactNode;
  hasContent?: boolean;
};

export function StationBasedGuard({ children, hasContent = false, sx }: Props) {
  const { isExpired, station } = useStationContext();

  const expired = isExpired();

  if (!station.stationId || expired) {
    return hasContent ? (
      <Container component={MotionContainer} sx={{ textAlign: 'center', ...sx }}>
        <m.div variants={varBounce().in}>
          <Typography variant="h3" sx={{ mb: 2 }}>
            QR Scan Required
          </Typography>
        </m.div>

        <m.div variants={varBounce().in}>
          <Typography sx={{ color: 'text.secondary' }}>
            Please scan a station QR code to continue.
          </Typography>
        </m.div>

        <m.div variants={varBounce().in}>
          <ForbiddenIllustration sx={{ my: { xs: 5, sm: 10 } }} />
        </m.div>
      </Container>
    ) : null;
  }

  return <>{children}</>;
}
