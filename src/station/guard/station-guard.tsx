import { Box, Typography } from '@mui/material';

import { useStationContext } from '../hooks';

// ----------------------------------------------------------------------

type Props = {
  children: React.ReactNode;
};

export function StationGuard({ children }: Props) {
  const { isExpired, station } = useStationContext();

  if (!station.stationId || isExpired()) {
    return (
      <Box textAlign="center" mt={8}>
        <Typography variant="h6" gutterBottom>
          Scan a Shelf First
        </Typography>
        <Typography sx={{ color: 'text.secondary', mb: 2 }}>
          Please scan a station QR code to view the books.
        </Typography>
      </Box>
    );
  }

  return <>{children}</>;
}
