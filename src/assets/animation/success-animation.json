{"v": "5.7.6", "fr": 60, "ip": 0, "op": 76, "w": 1200, "h": 1200, "nm": "Paper_Guns", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Hose 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\nfunction parentTotal() {\n    var parentVal = 0;\n    var layerChain = 'thisLayer';\n    while (eval([layerChain][0]).hasParent) {\n        layerChain = $bm_sum(layerChain, '.parent');\n        parentVal = $bm_sum(parentVal, eval([layerChain][0]).rotation);\n    }\n    return parentVal;\n}\nvar r = 0;\nif (thisLayer.hasParent) {\n    r = $bm_neg(parentTotal());\n}\n$bm_rt = r;"}, "p": {"a": 0, "k": [600, 600, 0], "ix": 2, "l": 2, "x": "var $bm_rt;\nvar p = [\n    0,\n    0\n];\ntry {\n    if (thisLayer.hasParent) {\n        p = parent.fromComp([\n            0,\n            0,\n            0\n        ]);\n    }\n    $bm_rt = p;\n} catch (err) {\n    $bm_rt = p;\n}"}, "a": {"a": 0, "k": [0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2, "x": "var $bm_rt;\n$bm_rt = $bm_div(value, length(toComp([\n    0,\n    0\n]), toComp([\n    0.7071,\n    0.7071\n]))) || 0.001;"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sr", "sy": 1, "d": 1, "pt": {"a": 0, "k": 4, "ix": 3}, "p": {"a": 0, "k": [0, 0], "ix": 4}, "r": {"a": 0, "k": 0, "ix": 5}, "ir": {"a": 0, "k": 500, "ix": 6, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "is": {"a": 0, "k": 0, "ix": 8, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "or": {"a": 0, "k": 113, "ix": 7, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "os": {"a": 0, "k": 0, "ix": 9, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "ix": 1, "nm": "LineForCurve", "mn": "ADBE Vector Shape - Star", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "e": {"a": 0, "k": 0, "ix": 2, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "o": {"a": 0, "k": -90, "ix": 3, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "m": 1, "ix": 2, "nm": "<PERSON>", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "a": {"a": 0, "k": [0, 0], "ix": 1, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "s": {"a": 0, "k": [100, 100], "ix": 3, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "r": {"a": 0, "k": 45, "ix": 6, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "o": {"a": 0, "k": 100, "ix": 7, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "sk": {"a": 0, "k": 0, "ix": 4, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "sa": {"a": 0, "k": 0, "ix": 5, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "nm": "Transform"}], "nm": "Arc", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.439215716194, 0.356862745098, 0.384313755409, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 19, "ix": 5, "x": "var $bm_rt;\nvar sFac = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2')('Parent Scale');\n$bm_rt = Math.abs($bm_mul(value, sFac));"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('A');\n} catch (err) {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "BaseHose", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Style", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 2::<PERSON><PERSON>", "np": 0, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [135, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "A", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [200, 200], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 2::Shoulder", "np": 0, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "B", "np": 1, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "2.09", "np": 0, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false, "cl": "09"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Version", "np": 1, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "sr", "sy": 1, "d": 1, "pt": {"a": 0, "k": 4, "ix": 3}, "p": {"a": 0, "k": [0, 0], "ix": 4}, "r": {"a": 0, "k": 0, "ix": 5}, "ir": {"a": 0, "k": 200, "ix": 6, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('Inner Radius');\n} catch (err) {\n    $bm_rt = value;\n}"}, "is": {"a": 0, "k": 100, "ix": 8, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('Bend Radius');\n} catch (err) {\n    $bm_rt = value;\n}"}, "or": {"a": 0, "k": 200, "ix": 7, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('Outer Radius');\n} catch (err) {\n    $bm_rt = value;\n}"}, "os": {"a": 0, "k": 0, "ix": 9}, "ix": 1, "nm": "LineForCurve", "mn": "ADBE Vector Shape - Star", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0.01, "ix": 1}, "e": {"a": 0, "k": 24.99, "ix": 2}, "o": {"a": 0, "k": -90, "ix": 3, "x": "var $bm_rt;\n$bm_rt = -90;"}, "m": 1, "ix": 2, "nm": "<PERSON>", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1, "x": "var $bm_rt;\nvar s = thisProperty.propertyGroup(2)(2)(1)(7);\n$bm_rt = [\n    -s,\n    0\n];"}, "s": {"a": 0, "k": [100, 100], "ix": 3, "x": "var $bm_rt;\nvar flop;\ntry {\n    var eff = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    var bendDir = eff('Bend Direction');\n    var autoFlop = eff('AutoFlop');\n    flop = bendDir > 0 ? 1 : -1;\n    autoFlop > 0 ? 0 : flop *= -1;\n    var s = flop == 1 ? [\n        -100,\n        100\n    ] : [\n        100,\n        100\n    ];\n    if (eff('Parent Scale') < 0) {\n        s = [\n            $bm_neg(s[0]),\n            s[1]\n        ];\n    }\n    $bm_rt = s;\n} catch (err) {\n    $bm_rt = value;\n}\n;"}, "r": {"a": 0, "k": 45, "ix": 6, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    var baseRot = ctrl('Base Rotation');\n    var flop = content('Admin').content('ArcMath').transform.scale[0];\n    var rotOffset = flop < 0 ? -45 : 225;\n    $bm_rt = $bm_sum(baseRot, rotOffset);\n} catch (err) {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "ArcMath", "np": 2, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('A');\n} catch (err) {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Admin", "np": 4, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1440, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Layer 13", "parent": 11, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": -3.718, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 15.873, "s": [32.415, -144.568, 0], "to": [1.309, 6.537, 0], "ti": [-1.309, -6.537, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 22.223, "s": [40.27, -105.347, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 53.967, "s": [40.27, -105.347, 0], "to": [-1.309, -6.537, 0], "ti": [1.309, 6.537, 0]}, {"t": 60.31640625, "s": [32.415, -144.568, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [87.296, -1616.492, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[2.239, 0], [1.709, 1.708], [2.705, -2.505], [3.286, 3.5], [-3.477, 3.32], [-23.152, -23.156], [3.418, -3.418]], "o": [[-2.239, 0], [-16.138, -16.135], [-3.53, 3.267], [-3.293, -3.501], [9.783, -9.342], [3.418, 3.418], [-1.709, 1.708]], "v": [[109.955, -1602.245], [103.768, -1604.808], [70.619, -1606.75], [58.275, -1607.151], [58.58, -1619.45], [116.141, -1617.183], [116.141, -1604.808]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.439215689898, 0.35686275363, 0.384313732386, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1440, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Layer 41", "parent": 11, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 15.873, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 22.223, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 53.967, "s": [0]}, {"t": 60.31640625, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": -3.718, "ix": 10}, "p": {"a": 0, "k": [-62.22, -25.081, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-15.602, -1500.264, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 15.873, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 22.223, "s": [29, 54, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 53.967, "s": [29, 54, 100]}, {"t": 60.31640625, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[3.643, 0], [0.933, 0.317], [9.382, -0.269], [0.066, -0.02], [1.377, 4.63], [-4.631, 1.379], [-8.154, 0.215], [-0.43, -0.148], [1.558, -4.575]], "o": [[-0.935, 0], [-0.056, -0.017], [-5.771, 0.151], [-4.639, 1.381], [-1.379, -4.631], [0.364, -0.109], [12.644, -0.302], [4.575, 1.556], [-1.238, 3.64]], "v": [[1.998, -1490.104], [-0.819, -1490.573], [-18.092, -1492.911], [-30.704, -1490.766], [-41.588, -1496.656], [-35.7, -1507.538], [-18.551, -1510.406], [4.816, -1507.14], [10.282, -1496.038]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.439215689898, 0.35686275363, 0.384313732386, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1440, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Layer 40", "parent": 11, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": -3.718, "ix": 10}, "p": {"a": 0, "k": [-62.602, -24.536, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 15.873, "s": [100, 35, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 22.223, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 53.967, "s": [100, 100, 100]}, {"t": 60.31640625, "s": [100, 35, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [27.861, 39.37], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Ellipse Path 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.439215689898, 0.35686275363, 0.384313732386, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1440, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Layer 39", "parent": 11, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 15.873, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 22.223, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 53.967, "s": [100]}, {"t": 60.31640625, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": -3.718, "ix": 10}, "p": {"a": 0, "k": [39.768, -38.065, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-15.602, -1500.264, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 15.873, "s": [29, 54, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 22.223, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 53.967, "s": [100, 100, 100]}, {"t": 60.31640625, "s": [29, 54, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[3.643, 0], [0.933, 0.317], [9.382, -0.269], [0.066, -0.02], [1.377, 4.63], [-4.631, 1.379], [-8.154, 0.215], [-0.43, -0.148], [1.558, -4.575]], "o": [[-0.935, 0], [-0.056, -0.017], [-5.771, 0.151], [-4.639, 1.381], [-1.379, -4.631], [0.364, -0.109], [12.644, -0.302], [4.575, 1.556], [-1.238, 3.64]], "v": [[1.998, -1490.104], [-0.819, -1490.573], [-18.092, -1492.911], [-30.704, -1490.766], [-41.588, -1496.656], [-35.7, -1507.538], [-18.551, -1510.406], [4.816, -1507.14], [10.282, -1496.038]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.439215689898, 0.35686275363, 0.384313732386, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1440, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "Layer 28", "parent": 11, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": -3.718, "ix": 10}, "p": {"a": 0, "k": [39.386, -37.52, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 15.873, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 22.223, "s": [100, 35, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 53.967, "s": [100, 35, 100]}, {"t": 60.31640625, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [27.861, 39.37], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Ellipse Path 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.439215689898, 0.35686275363, 0.384313732386, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1440, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "Layer 14", "parent": 11, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": -3.718, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 15.873, "s": [-65.68, -92.236, 0], "to": [-1.004, -4.898, 0], "ti": [1.004, 4.898, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 22.223, "s": [-71.706, -121.624, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 53.967, "s": [-71.706, -121.624, 0], "to": [1.004, 4.898, 0], "ti": [-1.004, -4.898, 0]}, {"t": 60.31640625, "s": [-65.68, -92.236, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-16.77, -1590.436, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[2.529, 0], [1.628, 1.337], [-3.066, 3.735], [-11.023, -5.682], [2.217, -4.294], [4.297, 2.191], [11.123, -13.551]], "o": [[-1.953, 0], [-3.735, -3.065], [16.128, -19.651], [4.294, 2.214], [-2.212, 4.283], [-0.876, -0.442], [-1.729, 2.107]], "v": [[-37.518, -1576.418], [-43.065, -1578.405], [-44.276, -1590.719], [7.982, -1599.128], [11.747, -1587.341], [-0.009, -1583.558], [-30.751, -1579.616]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.439215689898, 0.35686275363, 0.384313732386, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1440, "st": 0, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "Layer 10", "parent": 11, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": -3.718, "ix": 10}, "p": {"a": 0, "k": [140.31, -35.351, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [47.797, 47.797], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Ellipse Path 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.988235294118, 0.870588235294, 0.847058823529, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1440, "st": 0, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "Layer 9", "parent": 11, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": -3.718, "ix": 10}, "p": {"a": 0, "k": [-143.839, 0.937, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [47.797, 47.797], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Ellipse Path 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.988235294118, 0.870588235294, 0.847058823529, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1440, "st": 0, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 4, "nm": "Layer 8", "parent": 11, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": -3.718, "ix": 10}, "p": {"a": 0, "k": [7.837, 38.547, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [50.895, -1435.356, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[3.345, 0], [1.514, 1.422], [-3.308, 3.523], [-3.523, -3.29], [-15.891, -7.347], [-31.331, 1.774], [-1.021, 1.721], [-4.155, -2.429], [2.405, -4.167], [86.565, -4.88]], "o": [[-61.504, 0], [-3.523, -3.308], [3.298, -3.519], [0.193, 0.177], [14.675, 6.783], [76.536, -4.314], [2.456, -4.139], [4.15, 2.432], [-1.187, 2.052], [-3.503, 0.197]], "v": [[37.03, -1401.069], [-60.504, -1434.196], [-60.895, -1446.564], [-48.546, -1446.972], [-23.805, -1431.562], [46.317, -1418.832], [148.756, -1465.344], [160.717, -1468.45], [163.885, -1456.546], [47.303, -1401.359]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.439215689898, 0.35686275363, 0.384313732386, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1440, "st": 0, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 4, "nm": "null", "parent": 18, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 5.718, "ix": 10}, "p": {"a": 0, "k": [50.135, 2410.973, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [], "ip": 0, "op": 1440, "st": 0, "bm": 0}, {"ddd": 0, "ind": 12, "ty": 4, "nm": "Layer 37", "parent": 13, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.268], "y": [1]}, "o": {"x": [0.704], "y": [0]}, "t": 0, "s": [-139.418]}, {"i": {"x": [0.268], "y": [1]}, "o": {"x": [0.704], "y": [0]}, "t": 39, "s": [-112.418]}, {"t": 75, "s": [-139.418]}], "ix": 10}, "p": {"a": 0, "k": [-5.538, -19.314, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-237.558, -1393.901, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[12.596, 16.872], [0.174, 0.286], [-0.435, 8.529], [-0.898, 2.107], [3.804, 1.633], [1.636, -3.807], [0.427, -8.391], [-0.247, -2.375], [6.443, 0.157], [1.008, -0.07], [-0.281, -4.132], [-4.141, 0.278], [-9.935, -0.186], [-0.086, -0.939], [-2.35, -1.086], [-0.297, -2.217], [-3.699, 0], [-0.084, 0.003], [-0.535, -2.204], [-3.381, 0], [-0.593, 0.144], [0, 0]], "o": [[-0.131, -0.297], [-0.098, -0.181], [0.247, -4.83], [1.628, -3.805], [-3.801, -1.628], [-0.437, 1.021], [-0.151, 2.973], [-4.413, -0.031], [-12.905, -0.315], [-4.133, 0.281], [0.281, 4.135], [0.831, -0.025], [-0.24, 0.856], [0.253, 2.764], [-1.357, 1.568], [0.505, 3.767], [0.083, 0], [-1.222, 1.711], [0.83, 3.431], [0.583, 0], [0, 0], [9.028, -2.438]], "v": [[-247.367, -1417.471], [-247.789, -1418.356], [-249.767, -1431.395], [-246.242, -1444.849], [-250.177, -1454.694], [-260.028, -1450.761], [-264.747, -1432.156], [-264.547, -1424.146], [-283.634, -1424.408], [-307.68, -1424.622], [-314.74, -1416.628], [-306.835, -1409.648], [-288.96, -1409.52], [-289.225, -1406.818], [-284.887, -1400.717], [-286.674, -1394.831], [-279.249, -1388.326], [-278.997, -1388.342], [-280.209, -1382.233], [-272.926, -1376.5], [-271.154, -1376.712], [-245.258, -1382.992]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.439215689898, 0.35686275363, 0.384313732386, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1440, "st": 0, "bm": 0}, {"ddd": 0, "ind": 13, "ty": 4, "nm": "Hose 1::<PERSON><PERSON>", "parent": 18, "hd": true, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 2, "ix": 10, "x": "var $bm_rt;\nfunction parentTotal() {\n    var parentVal = 0;\n    var layerChain = 'thisLayer';\n    while (eval([layerChain][0]).hasParent) {\n        layerChain = $bm_sum(layerChain, '.parent');\n        parentVal = $bm_sum(parentVal, eval([layerChain][0]).rotation);\n    }\n    return parentVal;\n}\ntry {\n    var eff = thisLayer(4)('RubberHose 2');\n    var autoRotate = eff('Auto Rotate End');\n    if (autoRotate == 1) {\n        var a = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name).toComp([\n            0,\n            0,\n            0\n        ]);\n        var b = thisComp.layer(thisLayer(2)('Admin')(2)('B')(2)(1)._name).toComp([\n            0,\n            0,\n            0\n        ]);\n        var s = length(a, b);\n        var sFac = eff('Parent Scale');\n        var realism = eff('Realism');\n        var bendDir = $bm_div(eff('Bend Direction'), 100);\n        var hoseLength = $bm_div(eff('Hose Length'), 2);\n        var bendRad = eff('Bend Radius');\n        var autoFlop = eff('AutoFlop');\n        var baseRot = $bm_sub(180, radiansToDegrees(Math.atan2($bm_sub(b[0], a[0]), $bm_sub(b[1], a[1]))));\n        var outerRad = $bm_mul(Math.sin(0.78539816339), s);\n        var straight = $bm_div($bm_mul(1.4142135623731, outerRad), 2);\n        straight /= Math.max(Math.abs(sFac), 0.001);\n        var roundShrink = linear(Math.abs(bendRad), 0, 100, 1, 0.87);\n        var innerRad;\n        if (hoseLength > straight) {\n            innerRad = $bm_sum(straight, $bm_mul(Math.sqrt($bm_sub(Math.pow(hoseLength, 2), Math.pow(straight, 2))), roundShrink));\n            innerRad = linear(realism, 0, 100, hoseLength, innerRad);\n            innerRad = linear(Math.abs(bendDir), straight, innerRad);\n        } else {\n            innerRad = straight;\n        }\n        innerRad = linear(Math.abs(autoFlop), straight, innerRad);\n        var flopDir = 1;\n        if (bendDir < 0) {\n            flopDir = -1;\n        }\n        flopDir *= autoFlop;\n        var opp = $bm_mul($bm_sub(innerRad, straight), flopDir);\n        var theta = Math.atan($bm_div(opp, Math.max(straight, 0.001)));\n        var bendAngle = radiansToDegrees(theta);\n        if (sFac < 0) {\n            baseRot *= -1;\n        }\n        bendRad *= $bm_div($bm_div(theta, $bm_neg(Math.PI)), linear(s, hoseLength, 0, 2, 0.9));\n        var parentRot = hasParent ? parentTotal() : 0;\n        var rotCalc = $bm_sub($bm_sub($bm_sum(baseRot, bendAngle), bendRad), parentRot);\n        $bm_rt = $bm_sum(rotCalc, value);\n    } else {\n        $bm_rt = value;\n    }\n} catch (e) {\n    $bm_rt = value;\n}"}, "p": {"a": 1, "k": [{"i": {"x": 0.268, "y": 1}, "o": {"x": 0.704, "y": 0}, "t": 0, "s": [-219.413, 2513.356, 0], "to": [-18.019, -5.516, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.268, "y": 1}, "o": {"x": 0.704, "y": 0}, "t": 39, "s": [-327.525, 2480.259, 0], "to": [0, 0, 0], "ti": [-18.019, -5.516, 0]}, {"t": 75, "s": [-219.413, 2513.356, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "RubberHose 2", "np": 18, "mn": "Pseudo/3bf5uID/RubberHose_2", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Hose Length", "mn": "Pseudo/3bf5uID/RubberHose_2-0001", "ix": 1, "v": {"a": 0, "k": 290, "ix": 1}}, {"ty": 0, "nm": "Bend Radius", "mn": "Pseudo/3bf5uID/RubberHose_2-0002", "ix": 2, "v": {"a": 0, "k": 20, "ix": 2}}, {"ty": 0, "nm": "Realism", "mn": "Pseudo/3bf5uID/RubberHose_2-0003", "ix": 3, "v": {"a": 0, "k": 0, "ix": 3}}, {"ty": 0, "nm": "Bend Direction", "mn": "Pseudo/3bf5uID/RubberHose_2-0004", "ix": 4, "v": {"a": 0, "k": 100, "ix": 4}}, {"ty": 7, "nm": "Auto Rotate Start", "mn": "Pseudo/3bf5uID/RubberHose_2-0005", "ix": 5, "v": {"a": 0, "k": 1, "ix": 5}}, {"ty": 7, "nm": "Auto Rotate End", "mn": "Pseudo/3bf5uID/RubberHose_2-0006", "ix": 6, "v": {"a": 0, "k": 1, "ix": 6}}, {"ty": 6, "nm": "<PERSON>", "mn": "Pseudo/3bf5uID/RubberHose_2-0007", "ix": 7, "v": 0}, {"ty": 3, "nm": "A", "mn": "Pseudo/3bf5uID/RubberHose_2-0008", "ix": 8, "v": {"a": 0, "k": [0, 0], "ix": 8, "x": "var $bm_rt;\n$bm_rt = thisLayer.toComp([\n    0,\n    0,\n    0\n]);"}}, {"ty": 3, "nm": "B", "mn": "Pseudo/3bf5uID/RubberHose_2-0009", "ix": 9, "v": {"a": 0, "k": [0, 0], "ix": 9, "x": "var $bm_rt;\ntry {\n    var b = thisLayer(2)('Admin')(2)('B')(2)(1)._name;\n    $bm_rt = thisComp.layer(b).toComp([\n        0,\n        0,\n        0\n    ]);\n} catch (err) {\n    $bm_rt = value;\n}"}}, {"ty": 0, "nm": "Outer Radius", "mn": "Pseudo/3bf5uID/RubberHose_2-0010", "ix": 10, "v": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\nvar a = thisLayer(4)('RubberHose 2')('A');\nvar b = thisLayer(4)('RubberHose 2')('B');\nvar s = length(a, b);\n$bm_rt = $bm_mul(Math.sin(0.78539816339), s);"}}, {"ty": 0, "nm": "Inner Radius", "mn": "Pseudo/3bf5uID/RubberHose_2-0011", "ix": 11, "v": {"a": 0, "k": 0, "ix": 11, "x": "var $bm_rt;\nvar eff = thisLayer(4)('RubberHose 2');\nvar bendRad = eff('Bend Radius');\nvar hoseLength = $bm_div(eff('Hose Length'), 2);\nvar realism = eff('Realism');\nvar bendDir = $bm_div(eff('Bend Direction'), 100);\nvar sFac = eff('Parent Scale');\nvar straight = eff('Straight');\nvar autoFlop = eff('AutoFlop');\nvar roundShrink = linear(Math.abs(bendRad), 0, 100, 1, 0.87);\nvar innerRad;\nif (hoseLength > straight) {\n    innerRad = $bm_sum(straight, $bm_mul(Math.sqrt($bm_sub(Math.pow(hoseLength, 2), Math.pow(straight, 2))), roundShrink));\n    innerRad = linear(realism, 0, 100, hoseLength, innerRad);\n    innerRad = linear(Math.abs(bendDir), straight, innerRad);\n} else {\n    innerRad = straight;\n}\ninnerRad *= Math.abs(sFac);\ninnerRad = linear(Math.abs(autoFlop), $bm_mul(straight, Math.max(Math.abs(sFac), 0.001)), innerRad);\n$bm_rt = innerRad;"}}, {"ty": 0, "nm": "Straight", "mn": "Pseudo/3bf5uID/RubberHose_2-0012", "ix": 12, "v": {"a": 0, "k": 0, "ix": 12, "x": "var $bm_rt;\nvar sFac = thisLayer(4)('RubberHose 2')('Parent Scale');\nvar outerRad = $bm_div(thisLayer(4)('RubberHose 2')('Outer Radius'), Math.max(Math.abs(sFac), 0.001));\n;\n$bm_rt = $bm_div($bm_mul(1.4142135623731, outerRad), 2);"}}, {"ty": 0, "nm": "Base Rotation", "mn": "Pseudo/3bf5uID/RubberHose_2-0013", "ix": 13, "v": {"a": 0, "k": 0, "ix": 13, "x": "var $bm_rt;\nvar a = thisLayer(4)('RubberHose 2')('A');\nvar b = thisLayer(4)('RubberHose 2')('B');\n$bm_rt = radiansToDegrees(Math.atan2($bm_sub(a[1], b[1]), $bm_sub(a[0], b[0])));"}}, {"ty": 0, "nm": "AutoFlop", "mn": "Pseudo/3bf5uID/RubberHose_2-0014", "ix": 14, "v": {"a": 0, "k": 0, "ix": 14, "x": "var $bm_rt;\nvar hasAF = false, isEnabled = false, output;\ntry {\n    var lyrAF = thisComp.layer($bm_sum(thisLayer._name.split('::')[0], '::AutoFlop'));\n    isEnabled = lyrAF(4)('Enable')(1);\n    var falloffAngle = lyrAF(4)('Falloff')(1);\n    hasAF = true;\n    var a = thisLayer.toComp([\n        0,\n        0,\n        0\n    ]);\n    var b = thisComp.layer(thisLayer(2)('Admin')(2)('B')(2)(1)._name).toComp([\n        0,\n        0,\n        0\n    ]);\n} catch (e) {\n}\nif (hasAF && isEnabled == 1) {\n    var threshRot = lyrAF('ADBE Transform Group')('ADBE Rotate Z');\n    threshRot %= 360;\n    var ctrlAngle = $bm_neg(radiansToDegrees(Math.atan2($bm_sub(b[0], a[0]), $bm_sub(b[1], a[1]))));\n    var offsetAngle = $bm_sub(threshRot, ctrlAngle);\n    offsetAngle %= 360;\n    var sign = offsetAngle > 0 && offsetAngle < 180 || offsetAngle < -180 ? -1 : 1;\n    var absAngle = Math.abs(offsetAngle);\n    if (absAngle > 90) {\n        absAngle = Math.abs($bm_sub(absAngle, 180));\n    }\n    if (absAngle > 90) {\n        absAngle = Math.abs($bm_sub(absAngle, 180));\n    }\n    output = linear(absAngle, 0, falloffAngle, 0, 1);\n    output *= sign;\n} else {\n    output = 1;\n}\n$bm_rt = output;"}}, {"ty": 0, "nm": "Parent Scale", "mn": "Pseudo/3bf5uID/RubberHose_2-0015", "ix": 15, "v": {"a": 0, "k": 0, "ix": 15, "x": "var $bm_rt;\nvar sFactor = 1;\nvar scaleNorm = 0;\nvar layerChain = 'thisLayer';\nwhile (eval([layerChain][0]).hasParent) {\n    layerChain = $bm_sum(layerChain, '.parent');\n    scaleNorm = $bm_div(eval(layerChain)('ADBE Transform Group')('ADBE Scale')[0], 100);\n    sFactor = $bm_mul(sFactor, scaleNorm);\n}\n$bm_rt = sFactor;"}}, {"ty": 6, "nm": "", "mn": "Pseudo/3bf5uID/RubberHose_2-0016", "ix": 16, "v": 0}]}], "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [100, 100], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Circle", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-75, 0], [75, 0]], "c": false}, "ix": 2}, "nm": "01", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -30], [0, 30]], "c": false}, "ix": 2}, "nm": "02", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [30, 30], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "ControlShape", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.560000002384, 0, 1], "ix": 3, "x": "var $bm_rt;\nif (thisLayer.active) {\n    try {\n        var eff = thisLayer(4)('RubberHose 2');\n        var a = thisLayer.toComp([\n            0,\n            0,\n            0\n        ]);\n        var b = eff('B');\n        var straight = eff('Straight');\n        var hoseLength = $bm_div(eff('Hose Length'), 2);\n        if (straight > hoseLength) {\n            $bm_rt = [\n                0.51,\n                0.83,\n                0.98,\n                1\n            ];\n        } else {\n            $bm_rt = value;\n        }\n    } catch (err) {\n        $bm_rt = value;\n    }\n} else {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 3, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Control Point", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 1::<PERSON><PERSON>", "np": 0, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [135, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "A", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [20, 20], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 1::Shoulder", "np": 0, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "B", "np": 1, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "2.09", "np": 0, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false, "cl": "09"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Version", "np": 1, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Admin", "np": 3, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1440, "st": 0, "bm": 0}, {"ddd": 0, "ind": 14, "ty": 4, "nm": "Layer 38", "parent": 15, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.268], "y": [1]}, "o": {"x": [0.704], "y": [0]}, "t": 0, "s": [-83.532]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.704], "y": [0]}, "t": 39, "s": [-89.532]}, {"t": 75, "s": [-83.532]}], "ix": 10}, "p": {"a": 0, "k": [2.714, -12.331, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-237.558, -1393.901, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[15.678, 15.816], [0.174, 0.286], [-0.435, 8.529], [-0.898, 2.107], [3.804, 1.633], [1.636, -3.807], [0.427, -8.391], [-0.247, -2.375], [6.443, 0.157], [1.008, -0.07], [-0.281, -4.132], [-4.141, 0.278], [-9.935, -0.186], [-0.086, -0.939], [-2.35, -1.086], [-0.297, -2.217], [-3.699, 0], [-0.084, 0.003], [-0.535, -2.204], [-3.381, 0], [-0.593, 0.144], [0, 0]], "o": [[-0.131, -0.297], [-0.098, -0.181], [0.247, -4.83], [1.628, -3.805], [-3.801, -1.628], [-0.437, 1.021], [-0.151, 2.973], [-4.413, -0.031], [-12.905, -0.315], [-4.133, 0.281], [0.281, 4.135], [0.831, -0.025], [-0.24, 0.856], [0.253, 2.764], [-1.357, 1.568], [0.505, 3.767], [0.083, 0], [-1.222, 1.711], [0.83, 3.431], [0.583, 0], [0, 0], [12.633, -6.329]], "v": [[-247.367, -1417.471], [-247.789, -1418.356], [-249.767, -1431.395], [-246.242, -1444.849], [-250.177, -1454.694], [-260.028, -1450.761], [-264.747, -1432.156], [-264.547, -1424.146], [-283.634, -1424.408], [-307.68, -1424.622], [-314.74, -1416.628], [-306.835, -1409.648], [-288.96, -1409.52], [-289.225, -1406.818], [-284.887, -1400.717], [-286.674, -1394.831], [-279.249, -1388.326], [-278.997, -1388.342], [-280.209, -1382.233], [-272.926, -1376.5], [-271.154, -1376.712], [-244.687, -1384.104]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.439215689898, 0.35686275363, 0.384313732386, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1440, "st": 0, "bm": 0}, {"ddd": 0, "ind": 15, "ty": 4, "nm": "Hose 2::<PERSON><PERSON>", "hd": true, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\nfunction parentTotal() {\n    var parentVal = 0;\n    var layerChain = 'thisLayer';\n    while (eval([layerChain][0]).hasParent) {\n        layerChain = $bm_sum(layerChain, '.parent');\n        parentVal = $bm_sum(parentVal, eval([layerChain][0]).rotation);\n    }\n    return parentVal;\n}\ntry {\n    var eff = thisLayer(4)('RubberHose 2');\n    var autoRotate = eff('Auto Rotate End');\n    if (autoRotate == 1) {\n        var a = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name).toComp([\n            0,\n            0,\n            0\n        ]);\n        var b = thisComp.layer(thisLayer(2)('Admin')(2)('B')(2)(1)._name).toComp([\n            0,\n            0,\n            0\n        ]);\n        var s = length(a, b);\n        var sFac = eff('Parent Scale');\n        var realism = eff('Realism');\n        var bendDir = $bm_div(eff('Bend Direction'), 100);\n        var hoseLength = $bm_div(eff('Hose Length'), 2);\n        var bendRad = eff('Bend Radius');\n        var autoFlop = eff('AutoFlop');\n        var baseRot = $bm_sub(180, radiansToDegrees(Math.atan2($bm_sub(b[0], a[0]), $bm_sub(b[1], a[1]))));\n        var outerRad = $bm_mul(Math.sin(0.78539816339), s);\n        var straight = $bm_div($bm_mul(1.4142135623731, outerRad), 2);\n        straight /= Math.max(Math.abs(sFac), 0.001);\n        var roundShrink = linear(Math.abs(bendRad), 0, 100, 1, 0.87);\n        var innerRad;\n        if (hoseLength > straight) {\n            innerRad = $bm_sum(straight, $bm_mul(Math.sqrt($bm_sub(Math.pow(hoseLength, 2), Math.pow(straight, 2))), roundShrink));\n            innerRad = linear(realism, 0, 100, hoseLength, innerRad);\n            innerRad = linear(Math.abs(bendDir), straight, innerRad);\n        } else {\n            innerRad = straight;\n        }\n        innerRad = linear(Math.abs(autoFlop), straight, innerRad);\n        var flopDir = 1;\n        if (bendDir < 0) {\n            flopDir = -1;\n        }\n        flopDir *= autoFlop;\n        var opp = $bm_mul($bm_sub(innerRad, straight), flopDir);\n        var theta = Math.atan($bm_div(opp, Math.max(straight, 0.001)));\n        var bendAngle = radiansToDegrees(theta);\n        if (sFac < 0) {\n            baseRot *= -1;\n        }\n        bendRad *= $bm_div($bm_div(theta, $bm_neg(Math.PI)), linear(s, hoseLength, 0, 2, 0.9));\n        var parentRot = hasParent ? parentTotal() : 0;\n        var rotCalc = $bm_sub($bm_sub($bm_sum(baseRot, bendAngle), bendRad), parentRot);\n        $bm_rt = $bm_sum(rotCalc, value);\n    } else {\n        $bm_rt = value;\n    }\n} catch (e) {\n    $bm_rt = value;\n}"}, "p": {"a": 1, "k": [{"i": {"x": 0.268, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [722.5, 737, 0], "to": [15.667, -5, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.268, "y": 1}, "o": {"x": 0.704, "y": 0}, "t": 39, "s": [816.5, 707, 0], "to": [0, 0, 0], "ti": [15.667, -5, 0]}, {"t": 75, "s": [722.5, 737, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "RubberHose 2", "np": 18, "mn": "Pseudo/3bf5uID/RubberHose_2", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Hose Length", "mn": "Pseudo/3bf5uID/RubberHose_2-0001", "ix": 1, "v": {"a": 0, "k": 300, "ix": 1}}, {"ty": 0, "nm": "Bend Radius", "mn": "Pseudo/3bf5uID/RubberHose_2-0002", "ix": 2, "v": {"a": 0, "k": 15, "ix": 2}}, {"ty": 0, "nm": "Realism", "mn": "Pseudo/3bf5uID/RubberHose_2-0003", "ix": 3, "v": {"a": 0, "k": 0, "ix": 3}}, {"ty": 0, "nm": "Bend Direction", "mn": "Pseudo/3bf5uID/RubberHose_2-0004", "ix": 4, "v": {"a": 0, "k": 100, "ix": 4}}, {"ty": 7, "nm": "Auto Rotate Start", "mn": "Pseudo/3bf5uID/RubberHose_2-0005", "ix": 5, "v": {"a": 0, "k": 1, "ix": 5}}, {"ty": 7, "nm": "Auto Rotate End", "mn": "Pseudo/3bf5uID/RubberHose_2-0006", "ix": 6, "v": {"a": 0, "k": 1, "ix": 6}}, {"ty": 6, "nm": "<PERSON>", "mn": "Pseudo/3bf5uID/RubberHose_2-0007", "ix": 7, "v": 0}, {"ty": 3, "nm": "A", "mn": "Pseudo/3bf5uID/RubberHose_2-0008", "ix": 8, "v": {"a": 0, "k": [0, 0], "ix": 8, "x": "var $bm_rt;\n$bm_rt = thisLayer.toComp([\n    0,\n    0,\n    0\n]);"}}, {"ty": 3, "nm": "B", "mn": "Pseudo/3bf5uID/RubberHose_2-0009", "ix": 9, "v": {"a": 0, "k": [0, 0], "ix": 9, "x": "var $bm_rt;\ntry {\n    var b = thisLayer(2)('Admin')(2)('B')(2)(1)._name;\n    $bm_rt = thisComp.layer(b).toComp([\n        0,\n        0,\n        0\n    ]);\n} catch (err) {\n    $bm_rt = value;\n}"}}, {"ty": 0, "nm": "Outer Radius", "mn": "Pseudo/3bf5uID/RubberHose_2-0010", "ix": 10, "v": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\nvar a = thisLayer(4)('RubberHose 2')('A');\nvar b = thisLayer(4)('RubberHose 2')('B');\nvar s = length(a, b);\n$bm_rt = $bm_mul(Math.sin(0.78539816339), s);"}}, {"ty": 0, "nm": "Inner Radius", "mn": "Pseudo/3bf5uID/RubberHose_2-0011", "ix": 11, "v": {"a": 0, "k": 0, "ix": 11, "x": "var $bm_rt;\nvar eff = thisLayer(4)('RubberHose 2');\nvar bendRad = eff('Bend Radius');\nvar hoseLength = $bm_div(eff('Hose Length'), 2);\nvar realism = eff('Realism');\nvar bendDir = $bm_div(eff('Bend Direction'), 100);\nvar sFac = eff('Parent Scale');\nvar straight = eff('Straight');\nvar autoFlop = eff('AutoFlop');\nvar roundShrink = linear(Math.abs(bendRad), 0, 100, 1, 0.87);\nvar innerRad;\nif (hoseLength > straight) {\n    innerRad = $bm_sum(straight, $bm_mul(Math.sqrt($bm_sub(Math.pow(hoseLength, 2), Math.pow(straight, 2))), roundShrink));\n    innerRad = linear(realism, 0, 100, hoseLength, innerRad);\n    innerRad = linear(Math.abs(bendDir), straight, innerRad);\n} else {\n    innerRad = straight;\n}\ninnerRad *= Math.abs(sFac);\ninnerRad = linear(Math.abs(autoFlop), $bm_mul(straight, Math.max(Math.abs(sFac), 0.001)), innerRad);\n$bm_rt = innerRad;"}}, {"ty": 0, "nm": "Straight", "mn": "Pseudo/3bf5uID/RubberHose_2-0012", "ix": 12, "v": {"a": 0, "k": 0, "ix": 12, "x": "var $bm_rt;\nvar sFac = thisLayer(4)('RubberHose 2')('Parent Scale');\nvar outerRad = $bm_div(thisLayer(4)('RubberHose 2')('Outer Radius'), Math.max(Math.abs(sFac), 0.001));\n;\n$bm_rt = $bm_div($bm_mul(1.4142135623731, outerRad), 2);"}}, {"ty": 0, "nm": "Base Rotation", "mn": "Pseudo/3bf5uID/RubberHose_2-0013", "ix": 13, "v": {"a": 0, "k": 0, "ix": 13, "x": "var $bm_rt;\nvar a = thisLayer(4)('RubberHose 2')('A');\nvar b = thisLayer(4)('RubberHose 2')('B');\n$bm_rt = radiansToDegrees(Math.atan2($bm_sub(a[1], b[1]), $bm_sub(a[0], b[0])));"}}, {"ty": 0, "nm": "AutoFlop", "mn": "Pseudo/3bf5uID/RubberHose_2-0014", "ix": 14, "v": {"a": 0, "k": 0, "ix": 14, "x": "var $bm_rt;\nvar hasAF = false, isEnabled = false, output;\ntry {\n    var lyrAF = thisComp.layer($bm_sum(thisLayer._name.split('::')[0], '::AutoFlop'));\n    isEnabled = lyrAF(4)('Enable')(1);\n    var falloffAngle = lyrAF(4)('Falloff')(1);\n    hasAF = true;\n    var a = thisLayer.toComp([\n        0,\n        0,\n        0\n    ]);\n    var b = thisComp.layer(thisLayer(2)('Admin')(2)('B')(2)(1)._name).toComp([\n        0,\n        0,\n        0\n    ]);\n} catch (e) {\n}\nif (hasAF && isEnabled == 1) {\n    var threshRot = lyrAF('ADBE Transform Group')('ADBE Rotate Z');\n    threshRot %= 360;\n    var ctrlAngle = $bm_neg(radiansToDegrees(Math.atan2($bm_sub(b[0], a[0]), $bm_sub(b[1], a[1]))));\n    var offsetAngle = $bm_sub(threshRot, ctrlAngle);\n    offsetAngle %= 360;\n    var sign = offsetAngle > 0 && offsetAngle < 180 || offsetAngle < -180 ? -1 : 1;\n    var absAngle = Math.abs(offsetAngle);\n    if (absAngle > 90) {\n        absAngle = Math.abs($bm_sub(absAngle, 180));\n    }\n    if (absAngle > 90) {\n        absAngle = Math.abs($bm_sub(absAngle, 180));\n    }\n    output = linear(absAngle, 0, falloffAngle, 0, 1);\n    output *= sign;\n} else {\n    output = 1;\n}\n$bm_rt = output;"}}, {"ty": 0, "nm": "Parent Scale", "mn": "Pseudo/3bf5uID/RubberHose_2-0015", "ix": 15, "v": {"a": 0, "k": 0, "ix": 15, "x": "var $bm_rt;\nvar sFactor = 1;\nvar scaleNorm = 0;\nvar layerChain = 'thisLayer';\nwhile (eval([layerChain][0]).hasParent) {\n    layerChain = $bm_sum(layerChain, '.parent');\n    scaleNorm = $bm_div(eval(layerChain)('ADBE Transform Group')('ADBE Scale')[0], 100);\n    sFactor = $bm_mul(sFactor, scaleNorm);\n}\n$bm_rt = sFactor;"}}, {"ty": 6, "nm": "", "mn": "Pseudo/3bf5uID/RubberHose_2-0016", "ix": 16, "v": 0}]}], "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [100, 100], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Circle", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-75, 0], [75, 0]], "c": false}, "ix": 2}, "nm": "01", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -30], [0, 30]], "c": false}, "ix": 2}, "nm": "02", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [30, 30], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "ControlShape", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.560000002384, 0, 1], "ix": 3, "x": "var $bm_rt;\nif (thisLayer.active) {\n    try {\n        var eff = thisLayer(4)('RubberHose 2');\n        var a = thisLayer.toComp([\n            0,\n            0,\n            0\n        ]);\n        var b = eff('B');\n        var straight = eff('Straight');\n        var hoseLength = $bm_div(eff('Hose Length'), 2);\n        if (straight > hoseLength) {\n            $bm_rt = [\n                0.51,\n                0.83,\n                0.98,\n                1\n            ];\n        } else {\n            $bm_rt = value;\n        }\n    } catch (err) {\n        $bm_rt = value;\n    }\n} else {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 3, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Control Point", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 2::<PERSON><PERSON>", "np": 0, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [135, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "A", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [20, 20], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 2::Shoulder", "np": 0, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "B", "np": 1, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "2.09", "np": 0, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false, "cl": "09"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Version", "np": 1, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Admin", "np": 3, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1440, "st": 0, "bm": 0}, {"ddd": 0, "ind": 16, "ty": 4, "nm": "Hose 2::Shoulder", "parent": 18, "hd": true, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\nfunction parentTotal() {\n    var parentVal = 0;\n    var layerChain = 'thisLayer';\n    while (eval([layerChain][0]).hasParent) {\n        layerChain = $bm_sum(layerChain, '.parent');\n        parentVal = $bm_sum(parentVal, eval([layerChain][0]).rotation);\n    }\n    return parentVal;\n}\ntry {\n    var eff = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    var autoRotate = eff('Auto Rotate Start');\n    if (autoRotate == 1) {\n        var a = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name).toComp([\n            0,\n            0,\n            0\n        ]);\n        var b = thisComp.layer(thisLayer(2)('Admin')(2)('B')(2)(1)._name).toComp([\n            0,\n            0,\n            0\n        ]);\n        var s = length(a, b);\n        var sFac = eff('Parent Scale');\n        var realism = eff('Realism');\n        var bendDir = $bm_div(eff('Bend Direction'), 100);\n        var hoseLength = $bm_div(eff('Hose Length'), 2);\n        var bendRad = eff('Bend Radius');\n        var autoFlop = eff('AutoFlop');\n        var baseRot = $bm_sub(180, radiansToDegrees(Math.atan2($bm_sub(b[0], a[0]), $bm_sub(b[1], a[1]))));\n        var outerRad = $bm_mul(Math.sin(0.78539816339), s);\n        var straight = $bm_div($bm_mul(1.4142135623731, outerRad), 2);\n        straight /= Math.max(Math.abs(sFac), 0.001);\n        var roundShrink = linear(Math.abs(bendRad), 0, 100, 1, 0.87);\n        var innerRad;\n        if (hoseLength > straight) {\n            innerRad = $bm_sum(straight, $bm_mul(Math.sqrt($bm_sub(Math.pow(hoseLength, 2), Math.pow(straight, 2))), roundShrink));\n            innerRad = linear(realism, 0, 100, hoseLength, innerRad);\n            innerRad = linear(Math.abs(bendDir), straight, innerRad);\n        } else {\n            innerRad = straight;\n        }\n        innerRad = linear(Math.abs(autoFlop), straight, innerRad);\n        var flopDir = 1;\n        if (bendDir < 0) {\n            flopDir = -1;\n        }\n        flopDir *= autoFlop;\n        var opp = $bm_mul($bm_sub(innerRad, straight), flopDir);\n        var theta = Math.atan($bm_div(opp, Math.max(straight, 0.001)));\n        var bendAngle = radiansToDegrees(theta);\n        if (sFac < 0) {\n            baseRot *= -1;\n        }\n        bendRad *= $bm_div($bm_div(theta, $bm_neg(Math.PI)), linear(s, hoseLength, 0, 2, 0.9));\n        var parentRot = hasParent ? parentTotal() : 0;\n        var rotCalc = $bm_sub($bm_sum($bm_sub(baseRot, bendAngle), bendRad), parentRot);\n        $bm_rt = $bm_sum(rotCalc, value);\n    } else {\n        $bm_rt = value;\n    }\n} catch (e) {\n    $bm_rt = value;\n}"}, "p": {"a": 0, "k": [241.871, 2413.054, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [100, 100], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Circle", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-75, 0], [75, 0]], "c": false}, "ix": 2}, "nm": "01", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -30], [0, 30]], "c": false}, "ix": 2}, "nm": "02", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [30, 30], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "ControlShape", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.560000002384, 0, 1], "ix": 3, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name);\n    $bm_rt = ctrl(2)('Control Point')(2)('Stroke 1')('Color');\n} catch (e) {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 3, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Control Point", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 2::<PERSON><PERSON>", "np": 0, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [135, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "A", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [20, 20], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 2::Shoulder", "np": 0, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "B", "np": 1, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "2.09", "np": 0, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false, "cl": "09"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Version", "np": 1, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Admin", "np": 3, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1440, "st": 0, "bm": 0}, {"ddd": 0, "ind": 17, "ty": 4, "nm": "Layer 43", "parent": 18, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [186.726, 2203.744, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [186.726, 2203.744, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, -29.638], [-2.697, -13.214], [-12.534, 1.291], [-22.874, 0], [0, 0]], "o": [[0, 0], [0, 13.064], [2.683, 13.145], [12.363, -1.273], [25.565, 0], [0, 0]], "v": [[124.925, 2133.872], [122.403, 2193.148], [124.47, 2246.778], [149.76, 2268.517], [195.607, 2265.538], [251.112, 2273.616]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.776470601559, 0.921568632126, 0.956862747669, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1440, "st": 0, "bm": 0}, {"ddd": 0, "ind": 18, "ty": 4, "nm": "Paper", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.282], "y": [1]}, "o": {"x": [0.691], "y": [0]}, "t": 0, "s": [12]}, {"i": {"x": [0.263], "y": [1]}, "o": {"x": [0.709], "y": [0]}, "t": 38.889, "s": [0]}, {"t": 75, "s": [12]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [622.498, 819.317, 0], "to": [-3.333, 2.5, 0], "ti": [6, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 19, "s": [602.498, 834.317, 0], "to": [-6, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 38.889, "s": [586.498, 819.317, 0], "to": [0, 0, 0], "ti": [-6, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 57, "s": [602.498, 834.317, 0], "to": [6, 0, 0], "ti": [-3.333, 2.5, 0]}, {"t": 75, "s": [622.498, 819.317, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [56.993, 2639.366, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [0.511, 0.902, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 0, "s": [100, 102, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0.511, -0.102, 0]}, "t": 19, "s": [100, 97, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 38.889, "s": [100, 102, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 57, "s": [100, 97, 100]}, {"t": 75, "s": [100, 102, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[17.766, 0], [0, 0], [4.282, 21.851], [0, 80.989], [-11.439, 57.01], [-17.766, 0], [0, 0], [-3.936, -4.376], [0, 0], [0.38, -5.745], [0, -52.958], [-1.949, -48.844]], "o": [[0, 0], [-17.766, 0], [-10.297, -52.543], [0, -92.799], [5.834, -29.076], [0, 0], [5.681, 0], [0, 0], [3.735, 4.153], [-1.946, 29.411], [0, 88.772], [0.757, 18.964]], "v": [[224.764, 2640.862], [-87.405, 2640.862], [-124.253, 2606.493], [-142.973, 2387.127], [-121.913, 2164.239], [-82.725, 2129.87], [112.408, 2129.87], [127.459, 2136.72], [251.112, 2273.616], [254.592, 2291.565], [245.233, 2411.34], [256.932, 2606.493]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.568627476692, 0.803921580315, 0.858823537827, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1440, "st": 0, "bm": 0}, {"ddd": 0, "ind": 19, "ty": 4, "nm": "hand1", "parent": 13, "hd": true, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": -150.155, "ix": 10}, "p": {"a": 0, "k": [4.408, -15.7, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-662.419, -1677.644, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[3.137, 0], [0.789, 0.264], [-1.316, 3.928], [0, 0], [-3.923, -1.312], [1.316, -3.928], [0, 0]], "o": [[-0.789, 0], [-3.928, -1.315], [0, 0], [1.313, -3.927], [3.928, 1.315], [0, 0], [-1.05, 3.138]], "v": [[-649.843, -1672.225], [-652.221, -1672.614], [-656.955, -1682.106], [-647.756, -1709.611], [-638.266, -1714.345], [-633.532, -1704.853], [-642.731, -1677.348]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.439215689898, 0.35686275363, 0.384313732386, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 21", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[2.131, 3.552], [0, 0], [3.552, -2.131], [-2.131, -3.552], [0, 0], [3.428, -2.119], [-0.966, -3.176], [2.33, -1.732], [-2.471, -3.325], [0, 0], [0, 0], [3.04, -2.814], [-2.812, -3.041], [0, 0], [-2.019, 0], [-1.443, 1.334], [0.795, 2.656], [-0.393, 0], [-1.343, 0.997], [-0.051, 2.233], [-0.508, 0], [-1.23, 0.76], [1.313, 3.313], [-1.896, 0], [-1.208, 0.725]], "o": [[0, 0], [-2.131, -3.55], [-3.552, 2.131], [0, 0], [-2.248, -3.297], [-2.955, 1.83], [-2.441, -1.198], [-3.328, 2.469], [0, 0], [0, 0], [-2.81, -3.041], [-3.04, 2.812], [0, 0], [1.477, 1.598], [1.821, 0], [2.173, -2.01], [0.387, 0.061], [1.555, 0], [1.933, -1.435], [0.495, 0.101], [1.348, 0], [3.135, -1.942], [1.407, 1.332], [1.313, 0], [3.552, -2.131]], "v": [[-651.789, -1707.811], [-665.678, -1730.954], [-675.969, -1733.525], [-678.539, -1723.234], [-677.983, -1722.308], [-688.105, -1724.474], [-691.305, -1715.923], [-699.072, -1715.243], [-700.622, -1704.75], [-698.548, -1701.958], [-698.832, -1702.265], [-709.43, -1702.677], [-709.843, -1692.079], [-693.117, -1673.997], [-687.609, -1671.59], [-682.519, -1673.584], [-680.455, -1681.241], [-679.289, -1681.108], [-674.823, -1682.586], [-671.827, -1688.442], [-670.324, -1688.254], [-666.384, -1689.377], [-663.371, -1698.505], [-658.212, -1696.449], [-654.36, -1697.52]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.439215689898, 0.35686275363, 0.384313732386, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 22", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[6.257, 8.019], [16.555, -12.921], [-3.958, -14.816], [0, 0], [12.109, -9.453], [-12.92, -16.555], [-11.335, 0], [-6.943, 5.42], [2.263, 13.785], [0, 0], [-11.013, 8.597], [-1.245, 10.096]], "o": [[-12.917, -16.55], [-12.107, 9.449], [0, 0], [-13.406, -7.435], [-16.553, 12.919], [7.5, 9.608], [8.193, -0.001], [11.013, -8.595], [0, 0], [12.82, 5.541], [8.02, -6.257], [1.245, -10.095]], "v": [[-595.612, -1756.722], [-649.064, -1763.31], [-662.411, -1723.465], [-708.761, -1687.289], [-750.656, -1684.018], [-757.245, -1630.566], [-727.184, -1615.931], [-703.796, -1623.978], [-689.662, -1660.19], [-640.763, -1698.356], [-602.204, -1703.271], [-587.839, -1728.631]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.43137255311, 0.772549033165, 0.815686285496, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 23", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 24", "np": 0, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1440, "st": 0, "bm": 0}, {"ddd": 0, "ind": 20, "ty": 4, "nm": "Hose 1::Shoulder", "parent": 18, "hd": true, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\nfunction parentTotal() {\n    var parentVal = 0;\n    var layerChain = 'thisLayer';\n    while (eval([layerChain][0]).hasParent) {\n        layerChain = $bm_sum(layerChain, '.parent');\n        parentVal = $bm_sum(parentVal, eval([layerChain][0]).rotation);\n    }\n    return parentVal;\n}\ntry {\n    var eff = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    var autoRotate = eff('Auto Rotate Start');\n    if (autoRotate == 1) {\n        var a = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name).toComp([\n            0,\n            0,\n            0\n        ]);\n        var b = thisComp.layer(thisLayer(2)('Admin')(2)('B')(2)(1)._name).toComp([\n            0,\n            0,\n            0\n        ]);\n        var s = length(a, b);\n        var sFac = eff('Parent Scale');\n        var realism = eff('Realism');\n        var bendDir = $bm_div(eff('Bend Direction'), 100);\n        var hoseLength = $bm_div(eff('Hose Length'), 2);\n        var bendRad = eff('Bend Radius');\n        var autoFlop = eff('AutoFlop');\n        var baseRot = $bm_sub(180, radiansToDegrees(Math.atan2($bm_sub(b[0], a[0]), $bm_sub(b[1], a[1]))));\n        var outerRad = $bm_mul(Math.sin(0.78539816339), s);\n        var straight = $bm_div($bm_mul(1.4142135623731, outerRad), 2);\n        straight /= Math.max(Math.abs(sFac), 0.001);\n        var roundShrink = linear(Math.abs(bendRad), 0, 100, 1, 0.87);\n        var innerRad;\n        if (hoseLength > straight) {\n            innerRad = $bm_sum(straight, $bm_mul(Math.sqrt($bm_sub(Math.pow(hoseLength, 2), Math.pow(straight, 2))), roundShrink));\n            innerRad = linear(realism, 0, 100, hoseLength, innerRad);\n            innerRad = linear(Math.abs(bendDir), straight, innerRad);\n        } else {\n            innerRad = straight;\n        }\n        innerRad = linear(Math.abs(autoFlop), straight, innerRad);\n        var flopDir = 1;\n        if (bendDir < 0) {\n            flopDir = -1;\n        }\n        flopDir *= autoFlop;\n        var opp = $bm_mul($bm_sub(innerRad, straight), flopDir);\n        var theta = Math.atan($bm_div(opp, Math.max(straight, 0.001)));\n        var bendAngle = radiansToDegrees(theta);\n        if (sFac < 0) {\n            baseRot *= -1;\n        }\n        bendRad *= $bm_div($bm_div(theta, $bm_neg(Math.PI)), linear(s, hoseLength, 0, 2, 0.9));\n        var parentRot = hasParent ? parentTotal() : 0;\n        var rotCalc = $bm_sub($bm_sum($bm_sub(baseRot, bendAngle), bendRad), parentRot);\n        $bm_rt = $bm_sum(rotCalc, value);\n    } else {\n        $bm_rt = value;\n    }\n} catch (e) {\n    $bm_rt = value;\n}"}, "p": {"a": 0, "k": [-128.557, 2455.063, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [100, 100], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Circle", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-75, 0], [75, 0]], "c": false}, "ix": 2}, "nm": "01", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -30], [0, 30]], "c": false}, "ix": 2}, "nm": "02", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [30, 30], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "ControlShape", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.560000002384, 0, 1], "ix": 3, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name);\n    $bm_rt = ctrl(2)('Control Point')(2)('Stroke 1')('Color');\n} catch (e) {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 3, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Control Point", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 1::<PERSON><PERSON>", "np": 0, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [135, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "A", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [20, 20], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 1::Shoulder", "np": 0, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "B", "np": 1, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "2.09", "np": 0, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false, "cl": "09"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Version", "np": 1, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Admin", "np": 3, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1440, "st": 0, "bm": 0}, {"ddd": 0, "ind": 21, "ty": 4, "nm": "Hose 1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\nfunction parentTotal() {\n    var parentVal = 0;\n    var layerChain = 'thisLayer';\n    while (eval([layerChain][0]).hasParent) {\n        layerChain = $bm_sum(layerChain, '.parent');\n        parentVal = $bm_sum(parentVal, eval([layerChain][0]).rotation);\n    }\n    return parentVal;\n}\nvar r = 0;\nif (thisLayer.hasParent) {\n    r = $bm_neg(parentTotal());\n}\n$bm_rt = r;"}, "p": {"a": 0, "k": [600, 600, 0], "ix": 2, "l": 2, "x": "var $bm_rt;\nvar p = [\n    0,\n    0\n];\ntry {\n    if (thisLayer.hasParent) {\n        p = parent.fromComp([\n            0,\n            0,\n            0\n        ]);\n    }\n    $bm_rt = p;\n} catch (err) {\n    $bm_rt = p;\n}"}, "a": {"a": 0, "k": [0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2, "x": "var $bm_rt;\n$bm_rt = $bm_div(value, length(toComp([\n    0,\n    0\n]), toComp([\n    0.7071,\n    0.7071\n]))) || 0.001;"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sr", "sy": 1, "d": 1, "pt": {"a": 0, "k": 4, "ix": 3}, "p": {"a": 0, "k": [0, 0], "ix": 4}, "r": {"a": 0, "k": 0, "ix": 5}, "ir": {"a": 0, "k": 500, "ix": 6, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "is": {"a": 0, "k": 0, "ix": 8, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "or": {"a": 0, "k": 113, "ix": 7, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "os": {"a": 0, "k": 0, "ix": 9, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "ix": 1, "nm": "LineForCurve", "mn": "ADBE Vector Shape - Star", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "e": {"a": 0, "k": 0, "ix": 2, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "o": {"a": 0, "k": -90, "ix": 3, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "m": 1, "ix": 2, "nm": "<PERSON>", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "a": {"a": 0, "k": [0, 0], "ix": 1, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "s": {"a": 0, "k": [100, 100], "ix": 3, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "r": {"a": 0, "k": 45, "ix": 6, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "o": {"a": 0, "k": 100, "ix": 7, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "sk": {"a": 0, "k": 0, "ix": 4, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "sa": {"a": 0, "k": 0, "ix": 5, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "nm": "Transform"}], "nm": "Arc", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.439215716194, 0.356862745098, 0.384313755409, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 18, "ix": 5, "x": "var $bm_rt;\nvar sFac = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2')('Parent Scale');\n$bm_rt = Math.abs($bm_mul(value, sFac));"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('A');\n} catch (err) {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "BaseHose", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Style", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 1::<PERSON><PERSON>", "np": 0, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [135, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "A", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [200, 200], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 1::Shoulder", "np": 0, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "B", "np": 1, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "2.09", "np": 0, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false, "cl": "09"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Version", "np": 1, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "sr", "sy": 1, "d": 1, "pt": {"a": 0, "k": 4, "ix": 3}, "p": {"a": 0, "k": [0, 0], "ix": 4}, "r": {"a": 0, "k": 0, "ix": 5}, "ir": {"a": 0, "k": 200, "ix": 6, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('Inner Radius');\n} catch (err) {\n    $bm_rt = value;\n}"}, "is": {"a": 0, "k": 100, "ix": 8, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('Bend Radius');\n} catch (err) {\n    $bm_rt = value;\n}"}, "or": {"a": 0, "k": 200, "ix": 7, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('Outer Radius');\n} catch (err) {\n    $bm_rt = value;\n}"}, "os": {"a": 0, "k": 0, "ix": 9}, "ix": 1, "nm": "LineForCurve", "mn": "ADBE Vector Shape - Star", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0.01, "ix": 1}, "e": {"a": 0, "k": 24.99, "ix": 2}, "o": {"a": 0, "k": -90, "ix": 3, "x": "var $bm_rt;\n$bm_rt = -90;"}, "m": 1, "ix": 2, "nm": "<PERSON>", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1, "x": "var $bm_rt;\nvar s = thisProperty.propertyGroup(2)(2)(1)(7);\n$bm_rt = [\n    -s,\n    0\n];"}, "s": {"a": 0, "k": [100, 100], "ix": 3, "x": "var $bm_rt;\nvar flop;\ntry {\n    var eff = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    var bendDir = eff('Bend Direction');\n    var autoFlop = eff('AutoFlop');\n    flop = bendDir > 0 ? 1 : -1;\n    autoFlop > 0 ? 0 : flop *= -1;\n    var s = flop == 1 ? [\n        -100,\n        100\n    ] : [\n        100,\n        100\n    ];\n    if (eff('Parent Scale') < 0) {\n        s = [\n            $bm_neg(s[0]),\n            s[1]\n        ];\n    }\n    $bm_rt = s;\n} catch (err) {\n    $bm_rt = value;\n}\n;"}, "r": {"a": 0, "k": 45, "ix": 6, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    var baseRot = ctrl('Base Rotation');\n    var flop = content('Admin').content('ArcMath').transform.scale[0];\n    var rotOffset = flop < 0 ? -45 : 225;\n    $bm_rt = $bm_sum(baseRot, rotOffset);\n} catch (err) {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "ArcMath", "np": 2, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('A');\n} catch (err) {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Admin", "np": 4, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1440, "st": 0, "bm": 0}, {"ddd": 0, "ind": 22, "ty": 4, "nm": "<PERSON><PERSON><PERSON> 6", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": -0.137, "ix": 10}, "p": {"a": 0, "k": [551.366, 954.869, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [11.508, 4.957, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[13, 6.5], [-33.5, 5]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.439215716194, 0.356862745098, 0.384313755409, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 17, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 5, "k": {"a": 0, "k": [0, 0.379, 0.548, 0.885, 0.123, 0.351, 0.495, 0.784, 0.247, 0.323, 0.443, 0.683, 0.623, 0.206, 0.272, 0.402, 1, 0.09, 0.102, 0.122, 0, 1, 0.5, 0.5, 1, 0], "ix": 9}}, "s": {"a": 0, "k": [0, 0], "ix": 5}, "e": {"a": 0, "k": [100, 0], "ix": 6}, "t": 2, "h": {"a": 0, "k": 0, "ix": 7}, "a": {"a": 0, "k": 0, "ix": 8}, "nm": "Gradient Fill 1", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1440, "st": 0, "bm": 0}, {"ddd": 0, "ind": 23, "ty": 4, "nm": "Hose 3::<PERSON><PERSON>", "hd": true, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\nfunction parentTotal() {\n    var parentVal = 0;\n    var layerChain = 'thisLayer';\n    while (eval([layerChain][0]).hasParent) {\n        layerChain = $bm_sum(layerChain, '.parent');\n        parentVal = $bm_sum(parentVal, eval([layerChain][0]).rotation);\n    }\n    return parentVal;\n}\ntry {\n    var eff = thisLayer(4)('RubberHose 2');\n    var autoRotate = eff('Auto Rotate End');\n    if (autoRotate == 1) {\n        var a = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name).toComp([\n            0,\n            0,\n            0\n        ]);\n        var b = thisComp.layer(thisLayer(2)('Admin')(2)('B')(2)(1)._name).toComp([\n            0,\n            0,\n            0\n        ]);\n        var s = length(a, b);\n        var sFac = eff('Parent Scale');\n        var realism = eff('Realism');\n        var bendDir = $bm_div(eff('Bend Direction'), 100);\n        var hoseLength = $bm_div(eff('Hose Length'), 2);\n        var bendRad = eff('Bend Radius');\n        var autoFlop = eff('AutoFlop');\n        var baseRot = $bm_sub(180, radiansToDegrees(Math.atan2($bm_sub(b[0], a[0]), $bm_sub(b[1], a[1]))));\n        var outerRad = $bm_mul(Math.sin(0.78539816339), s);\n        var straight = $bm_div($bm_mul(1.4142135623731, outerRad), 2);\n        straight /= Math.max(Math.abs(sFac), 0.001);\n        var roundShrink = linear(Math.abs(bendRad), 0, 100, 1, 0.87);\n        var innerRad;\n        if (hoseLength > straight) {\n            innerRad = $bm_sum(straight, $bm_mul(Math.sqrt($bm_sub(Math.pow(hoseLength, 2), Math.pow(straight, 2))), roundShrink));\n            innerRad = linear(realism, 0, 100, hoseLength, innerRad);\n            innerRad = linear(Math.abs(bendDir), straight, innerRad);\n        } else {\n            innerRad = straight;\n        }\n        innerRad = linear(Math.abs(autoFlop), straight, innerRad);\n        var flopDir = 1;\n        if (bendDir < 0) {\n            flopDir = -1;\n        }\n        flopDir *= autoFlop;\n        var opp = $bm_mul($bm_sub(innerRad, straight), flopDir);\n        var theta = Math.atan($bm_div(opp, Math.max(straight, 0.001)));\n        var bendAngle = radiansToDegrees(theta);\n        if (sFac < 0) {\n            baseRot *= -1;\n        }\n        bendRad *= $bm_div($bm_div(theta, $bm_neg(Math.PI)), linear(s, hoseLength, 0, 2, 0.9));\n        var parentRot = hasParent ? parentTotal() : 0;\n        var rotCalc = $bm_sub($bm_sub($bm_sum(baseRot, bendAngle), bendRad), parentRot);\n        $bm_rt = $bm_sum(rotCalc, value);\n    } else {\n        $bm_rt = value;\n    }\n} catch (e) {\n    $bm_rt = value;\n}"}, "p": {"a": 0, "k": [552, 954, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "RubberHose 2", "np": 18, "mn": "Pseudo/3bf5uID/RubberHose_2", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Hose Length", "mn": "Pseudo/3bf5uID/RubberHose_2-0001", "ix": 1, "v": {"a": 0, "k": 240, "ix": 1}}, {"ty": 0, "nm": "Bend Radius", "mn": "Pseudo/3bf5uID/RubberHose_2-0002", "ix": 2, "v": {"a": 0, "k": 100, "ix": 2}}, {"ty": 0, "nm": "Realism", "mn": "Pseudo/3bf5uID/RubberHose_2-0003", "ix": 3, "v": {"a": 0, "k": 0, "ix": 3}}, {"ty": 0, "nm": "Bend Direction", "mn": "Pseudo/3bf5uID/RubberHose_2-0004", "ix": 4, "v": {"a": 0, "k": -4, "ix": 4}}, {"ty": 7, "nm": "Auto Rotate Start", "mn": "Pseudo/3bf5uID/RubberHose_2-0005", "ix": 5, "v": {"a": 0, "k": 1, "ix": 5}}, {"ty": 7, "nm": "Auto Rotate End", "mn": "Pseudo/3bf5uID/RubberHose_2-0006", "ix": 6, "v": {"a": 0, "k": 1, "ix": 6}}, {"ty": 6, "nm": "<PERSON>", "mn": "Pseudo/3bf5uID/RubberHose_2-0007", "ix": 7, "v": 0}, {"ty": 3, "nm": "A", "mn": "Pseudo/3bf5uID/RubberHose_2-0008", "ix": 8, "v": {"a": 0, "k": [0, 0], "ix": 8, "x": "var $bm_rt;\n$bm_rt = thisLayer.toComp([\n    0,\n    0,\n    0\n]);"}}, {"ty": 3, "nm": "B", "mn": "Pseudo/3bf5uID/RubberHose_2-0009", "ix": 9, "v": {"a": 0, "k": [0, 0], "ix": 9, "x": "var $bm_rt;\ntry {\n    var b = thisLayer(2)('Admin')(2)('B')(2)(1)._name;\n    $bm_rt = thisComp.layer(b).toComp([\n        0,\n        0,\n        0\n    ]);\n} catch (err) {\n    $bm_rt = value;\n}"}}, {"ty": 0, "nm": "Outer Radius", "mn": "Pseudo/3bf5uID/RubberHose_2-0010", "ix": 10, "v": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\nvar a = thisLayer(4)('RubberHose 2')('A');\nvar b = thisLayer(4)('RubberHose 2')('B');\nvar s = length(a, b);\n$bm_rt = $bm_mul(Math.sin(0.78539816339), s);"}}, {"ty": 0, "nm": "Inner Radius", "mn": "Pseudo/3bf5uID/RubberHose_2-0011", "ix": 11, "v": {"a": 0, "k": 0, "ix": 11, "x": "var $bm_rt;\nvar eff = thisLayer(4)('RubberHose 2');\nvar bendRad = eff('Bend Radius');\nvar hoseLength = $bm_div(eff('Hose Length'), 2);\nvar realism = eff('Realism');\nvar bendDir = $bm_div(eff('Bend Direction'), 100);\nvar sFac = eff('Parent Scale');\nvar straight = eff('Straight');\nvar autoFlop = eff('AutoFlop');\nvar roundShrink = linear(Math.abs(bendRad), 0, 100, 1, 0.87);\nvar innerRad;\nif (hoseLength > straight) {\n    innerRad = $bm_sum(straight, $bm_mul(Math.sqrt($bm_sub(Math.pow(hoseLength, 2), Math.pow(straight, 2))), roundShrink));\n    innerRad = linear(realism, 0, 100, hoseLength, innerRad);\n    innerRad = linear(Math.abs(bendDir), straight, innerRad);\n} else {\n    innerRad = straight;\n}\ninnerRad *= Math.abs(sFac);\ninnerRad = linear(Math.abs(autoFlop), $bm_mul(straight, Math.max(Math.abs(sFac), 0.001)), innerRad);\n$bm_rt = innerRad;"}}, {"ty": 0, "nm": "Straight", "mn": "Pseudo/3bf5uID/RubberHose_2-0012", "ix": 12, "v": {"a": 0, "k": 0, "ix": 12, "x": "var $bm_rt;\nvar sFac = thisLayer(4)('RubberHose 2')('Parent Scale');\nvar outerRad = $bm_div(thisLayer(4)('RubberHose 2')('Outer Radius'), Math.max(Math.abs(sFac), 0.001));\n;\n$bm_rt = $bm_div($bm_mul(1.4142135623731, outerRad), 2);"}}, {"ty": 0, "nm": "Base Rotation", "mn": "Pseudo/3bf5uID/RubberHose_2-0013", "ix": 13, "v": {"a": 0, "k": 0, "ix": 13, "x": "var $bm_rt;\nvar a = thisLayer(4)('RubberHose 2')('A');\nvar b = thisLayer(4)('RubberHose 2')('B');\n$bm_rt = radiansToDegrees(Math.atan2($bm_sub(a[1], b[1]), $bm_sub(a[0], b[0])));"}}, {"ty": 0, "nm": "AutoFlop", "mn": "Pseudo/3bf5uID/RubberHose_2-0014", "ix": 14, "v": {"a": 0, "k": 0, "ix": 14, "x": "var $bm_rt;\nvar hasAF = false, isEnabled = false, output;\ntry {\n    var lyrAF = thisComp.layer($bm_sum(thisLayer._name.split('::')[0], '::AutoFlop'));\n    isEnabled = lyrAF(4)('Enable')(1);\n    var falloffAngle = lyrAF(4)('Falloff')(1);\n    hasAF = true;\n    var a = thisLayer.toComp([\n        0,\n        0,\n        0\n    ]);\n    var b = thisComp.layer(thisLayer(2)('Admin')(2)('B')(2)(1)._name).toComp([\n        0,\n        0,\n        0\n    ]);\n} catch (e) {\n}\nif (hasAF && isEnabled == 1) {\n    var threshRot = lyrAF('ADBE Transform Group')('ADBE Rotate Z');\n    threshRot %= 360;\n    var ctrlAngle = $bm_neg(radiansToDegrees(Math.atan2($bm_sub(b[0], a[0]), $bm_sub(b[1], a[1]))));\n    var offsetAngle = $bm_sub(threshRot, ctrlAngle);\n    offsetAngle %= 360;\n    var sign = offsetAngle > 0 && offsetAngle < 180 || offsetAngle < -180 ? -1 : 1;\n    var absAngle = Math.abs(offsetAngle);\n    if (absAngle > 90) {\n        absAngle = Math.abs($bm_sub(absAngle, 180));\n    }\n    if (absAngle > 90) {\n        absAngle = Math.abs($bm_sub(absAngle, 180));\n    }\n    output = linear(absAngle, 0, falloffAngle, 0, 1);\n    output *= sign;\n} else {\n    output = 1;\n}\n$bm_rt = output;"}}, {"ty": 0, "nm": "Parent Scale", "mn": "Pseudo/3bf5uID/RubberHose_2-0015", "ix": 15, "v": {"a": 0, "k": 0, "ix": 15, "x": "var $bm_rt;\nvar sFactor = 1;\nvar scaleNorm = 0;\nvar layerChain = 'thisLayer';\nwhile (eval([layerChain][0]).hasParent) {\n    layerChain = $bm_sum(layerChain, '.parent');\n    scaleNorm = $bm_div(eval(layerChain)('ADBE Transform Group')('ADBE Scale')[0], 100);\n    sFactor = $bm_mul(sFactor, scaleNorm);\n}\n$bm_rt = sFactor;"}}, {"ty": 6, "nm": "", "mn": "Pseudo/3bf5uID/RubberHose_2-0016", "ix": 16, "v": 0}]}], "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [100, 100], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Circle", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-75, 0], [75, 0]], "c": false}, "ix": 2}, "nm": "01", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -30], [0, 30]], "c": false}, "ix": 2}, "nm": "02", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [30, 30], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "ControlShape", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.560000002384, 0, 1], "ix": 3, "x": "var $bm_rt;\nif (thisLayer.active) {\n    try {\n        var eff = thisLayer(4)('RubberHose 2');\n        var a = thisLayer.toComp([\n            0,\n            0,\n            0\n        ]);\n        var b = eff('B');\n        var straight = eff('Straight');\n        var hoseLength = $bm_div(eff('Hose Length'), 2);\n        if (straight > hoseLength) {\n            $bm_rt = [\n                0.51,\n                0.83,\n                0.98,\n                1\n            ];\n        } else {\n            $bm_rt = value;\n        }\n    } catch (err) {\n        $bm_rt = value;\n    }\n} else {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 3, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Control Point", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 3::<PERSON><PERSON>", "np": 0, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [135, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "A", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [20, 20], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 3::Shoulder", "np": 0, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "B", "np": 1, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "2.09", "np": 0, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false, "cl": "09"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Version", "np": 1, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Admin", "np": 3, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1440, "st": 0, "bm": 0}, {"ddd": 0, "ind": 24, "ty": 4, "nm": "Hose 3::Shoulder", "parent": 18, "hd": true, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\nfunction parentTotal() {\n    var parentVal = 0;\n    var layerChain = 'thisLayer';\n    while (eval([layerChain][0]).hasParent) {\n        layerChain = $bm_sum(layerChain, '.parent');\n        parentVal = $bm_sum(parentVal, eval([layerChain][0]).rotation);\n    }\n    return parentVal;\n}\ntry {\n    var eff = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    var autoRotate = eff('Auto Rotate Start');\n    if (autoRotate == 1) {\n        var a = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name).toComp([\n            0,\n            0,\n            0\n        ]);\n        var b = thisComp.layer(thisLayer(2)('Admin')(2)('B')(2)(1)._name).toComp([\n            0,\n            0,\n            0\n        ]);\n        var s = length(a, b);\n        var sFac = eff('Parent Scale');\n        var realism = eff('Realism');\n        var bendDir = $bm_div(eff('Bend Direction'), 100);\n        var hoseLength = $bm_div(eff('Hose Length'), 2);\n        var bendRad = eff('Bend Radius');\n        var autoFlop = eff('AutoFlop');\n        var baseRot = $bm_sub(180, radiansToDegrees(Math.atan2($bm_sub(b[0], a[0]), $bm_sub(b[1], a[1]))));\n        var outerRad = $bm_mul(Math.sin(0.78539816339), s);\n        var straight = $bm_div($bm_mul(1.4142135623731, outerRad), 2);\n        straight /= Math.max(Math.abs(sFac), 0.001);\n        var roundShrink = linear(Math.abs(bendRad), 0, 100, 1, 0.87);\n        var innerRad;\n        if (hoseLength > straight) {\n            innerRad = $bm_sum(straight, $bm_mul(Math.sqrt($bm_sub(Math.pow(hoseLength, 2), Math.pow(straight, 2))), roundShrink));\n            innerRad = linear(realism, 0, 100, hoseLength, innerRad);\n            innerRad = linear(Math.abs(bendDir), straight, innerRad);\n        } else {\n            innerRad = straight;\n        }\n        innerRad = linear(Math.abs(autoFlop), straight, innerRad);\n        var flopDir = 1;\n        if (bendDir < 0) {\n            flopDir = -1;\n        }\n        flopDir *= autoFlop;\n        var opp = $bm_mul($bm_sub(innerRad, straight), flopDir);\n        var theta = Math.atan($bm_div(opp, Math.max(straight, 0.001)));\n        var bendAngle = radiansToDegrees(theta);\n        if (sFac < 0) {\n            baseRot *= -1;\n        }\n        bendRad *= $bm_div($bm_div(theta, $bm_neg(Math.PI)), linear(s, hoseLength, 0, 2, 0.9));\n        var parentRot = hasParent ? parentTotal() : 0;\n        var rotCalc = $bm_sub($bm_sum($bm_sub(baseRot, bendAngle), bendRad), parentRot);\n        $bm_rt = $bm_sum(rotCalc, value);\n    } else {\n        $bm_rt = value;\n    }\n} catch (e) {\n    $bm_rt = value;\n}"}, "p": {"a": 0, "k": [16.283, 2636.015, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [100, 100], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Circle", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-75, 0], [75, 0]], "c": false}, "ix": 2}, "nm": "01", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -30], [0, 30]], "c": false}, "ix": 2}, "nm": "02", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [30, 30], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "ControlShape", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.560000002384, 0, 1], "ix": 3, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name);\n    $bm_rt = ctrl(2)('Control Point')(2)('Stroke 1')('Color');\n} catch (e) {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 3, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Control Point", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 3::<PERSON><PERSON>", "np": 0, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [135, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "A", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [20, 20], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 3::Shoulder", "np": 0, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "B", "np": 1, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "2.09", "np": 0, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false, "cl": "09"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Version", "np": 1, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Admin", "np": 3, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1440, "st": 0, "bm": 0}, {"ddd": 0, "ind": 25, "ty": 4, "nm": "Hose 3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\nfunction parentTotal() {\n    var parentVal = 0;\n    var layerChain = 'thisLayer';\n    while (eval([layerChain][0]).hasParent) {\n        layerChain = $bm_sum(layerChain, '.parent');\n        parentVal = $bm_sum(parentVal, eval([layerChain][0]).rotation);\n    }\n    return parentVal;\n}\nvar r = 0;\nif (thisLayer.hasParent) {\n    r = $bm_neg(parentTotal());\n}\n$bm_rt = r;"}, "p": {"a": 0, "k": [600, 600, 0], "ix": 2, "l": 2, "x": "var $bm_rt;\nvar p = [\n    0,\n    0\n];\ntry {\n    if (thisLayer.hasParent) {\n        p = parent.fromComp([\n            0,\n            0,\n            0\n        ]);\n    }\n    $bm_rt = p;\n} catch (err) {\n    $bm_rt = p;\n}"}, "a": {"a": 0, "k": [0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2, "x": "var $bm_rt;\n$bm_rt = $bm_div(value, length(toComp([\n    0,\n    0\n]), toComp([\n    0.7071,\n    0.7071\n]))) || 0.001;"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sr", "sy": 1, "d": 1, "pt": {"a": 0, "k": 4, "ix": 3}, "p": {"a": 0, "k": [0, 0], "ix": 4}, "r": {"a": 0, "k": 0, "ix": 5}, "ir": {"a": 0, "k": 500, "ix": 6, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "is": {"a": 0, "k": 0, "ix": 8, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "or": {"a": 0, "k": 113, "ix": 7, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "os": {"a": 0, "k": 0, "ix": 9, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "ix": 1, "nm": "LineForCurve", "mn": "ADBE Vector Shape - Star", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "e": {"a": 0, "k": 0, "ix": 2, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "o": {"a": 0, "k": -90, "ix": 3, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "m": 1, "ix": 2, "nm": "<PERSON>", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "a": {"a": 0, "k": [0, 0], "ix": 1, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "s": {"a": 0, "k": [100, 100], "ix": 3, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "r": {"a": 0, "k": 45, "ix": 6, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "o": {"a": 0, "k": 100, "ix": 7, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "sk": {"a": 0, "k": 0, "ix": 4, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "sa": {"a": 0, "k": 0, "ix": 5, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "nm": "Transform"}], "nm": "Arc", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.439215716194, 0.356862745098, 0.384313755409, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 19, "ix": 5, "x": "var $bm_rt;\nvar sFac = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2')('Parent Scale');\n$bm_rt = Math.abs($bm_mul(value, sFac));"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('A');\n} catch (err) {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "BaseHose", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Style", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 3::<PERSON><PERSON>", "np": 0, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [135, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "A", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [200, 200], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 3::Shoulder", "np": 0, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "B", "np": 1, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "2.09", "np": 0, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false, "cl": "09"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Version", "np": 1, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "sr", "sy": 1, "d": 1, "pt": {"a": 0, "k": 4, "ix": 3}, "p": {"a": 0, "k": [0, 0], "ix": 4}, "r": {"a": 0, "k": 0, "ix": 5}, "ir": {"a": 0, "k": 200, "ix": 6, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('Inner Radius');\n} catch (err) {\n    $bm_rt = value;\n}"}, "is": {"a": 0, "k": 100, "ix": 8, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('Bend Radius');\n} catch (err) {\n    $bm_rt = value;\n}"}, "or": {"a": 0, "k": 200, "ix": 7, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('Outer Radius');\n} catch (err) {\n    $bm_rt = value;\n}"}, "os": {"a": 0, "k": 0, "ix": 9}, "ix": 1, "nm": "LineForCurve", "mn": "ADBE Vector Shape - Star", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0.01, "ix": 1}, "e": {"a": 0, "k": 24.99, "ix": 2}, "o": {"a": 0, "k": -90, "ix": 3, "x": "var $bm_rt;\n$bm_rt = -90;"}, "m": 1, "ix": 2, "nm": "<PERSON>", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1, "x": "var $bm_rt;\nvar s = thisProperty.propertyGroup(2)(2)(1)(7);\n$bm_rt = [\n    -s,\n    0\n];"}, "s": {"a": 0, "k": [100, 100], "ix": 3, "x": "var $bm_rt;\nvar flop;\ntry {\n    var eff = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    var bendDir = eff('Bend Direction');\n    var autoFlop = eff('AutoFlop');\n    flop = bendDir > 0 ? 1 : -1;\n    autoFlop > 0 ? 0 : flop *= -1;\n    var s = flop == 1 ? [\n        -100,\n        100\n    ] : [\n        100,\n        100\n    ];\n    if (eff('Parent Scale') < 0) {\n        s = [\n            $bm_neg(s[0]),\n            s[1]\n        ];\n    }\n    $bm_rt = s;\n} catch (err) {\n    $bm_rt = value;\n}\n;"}, "r": {"a": 0, "k": 45, "ix": 6, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    var baseRot = ctrl('Base Rotation');\n    var flop = content('Admin').content('ArcMath').transform.scale[0];\n    var rotOffset = flop < 0 ? -45 : 225;\n    $bm_rt = $bm_sum(baseRot, rotOffset);\n} catch (err) {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "ArcMath", "np": 2, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('A');\n} catch (err) {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Admin", "np": 4, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1440, "st": 0, "bm": 0}, {"ddd": 0, "ind": 26, "ty": 4, "nm": "S<PERSON>pe Layer 5", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": -0.265, "ix": 10}, "p": {"a": 0, "k": [661.192, 960.126, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [123.342, 5.642, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[169, 6.5], [122.5, 5]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.439215716194, 0.356862745098, 0.384313755409, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 17, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 5, "k": {"a": 0, "k": [0, 0.379, 0.548, 0.885, 0.123, 0.351, 0.495, 0.784, 0.247, 0.323, 0.443, 0.683, 0.623, 0.206, 0.272, 0.402, 1, 0.09, 0.102, 0.122, 0, 1, 0.5, 0.5, 1, 0], "ix": 9}}, "s": {"a": 0, "k": [0, 0], "ix": 5}, "e": {"a": 0, "k": [100, 0], "ix": 6}, "t": 2, "h": {"a": 0, "k": 0, "ix": 7}, "a": {"a": 0, "k": 0, "ix": 8}, "nm": "Gradient Fill 1", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1440, "st": 0, "bm": 0}, {"ddd": 0, "ind": 27, "ty": 4, "nm": "Hose 4::<PERSON><PERSON>", "hd": true, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\nfunction parentTotal() {\n    var parentVal = 0;\n    var layerChain = 'thisLayer';\n    while (eval([layerChain][0]).hasParent) {\n        layerChain = $bm_sum(layerChain, '.parent');\n        parentVal = $bm_sum(parentVal, eval([layerChain][0]).rotation);\n    }\n    return parentVal;\n}\ntry {\n    var eff = thisLayer(4)('RubberHose 2');\n    var autoRotate = eff('Auto Rotate End');\n    if (autoRotate == 1) {\n        var a = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name).toComp([\n            0,\n            0,\n            0\n        ]);\n        var b = thisComp.layer(thisLayer(2)('Admin')(2)('B')(2)(1)._name).toComp([\n            0,\n            0,\n            0\n        ]);\n        var s = length(a, b);\n        var sFac = eff('Parent Scale');\n        var realism = eff('Realism');\n        var bendDir = $bm_div(eff('Bend Direction'), 100);\n        var hoseLength = $bm_div(eff('Hose Length'), 2);\n        var bendRad = eff('Bend Radius');\n        var autoFlop = eff('AutoFlop');\n        var baseRot = $bm_sub(180, radiansToDegrees(Math.atan2($bm_sub(b[0], a[0]), $bm_sub(b[1], a[1]))));\n        var outerRad = $bm_mul(Math.sin(0.78539816339), s);\n        var straight = $bm_div($bm_mul(1.4142135623731, outerRad), 2);\n        straight /= Math.max(Math.abs(sFac), 0.001);\n        var roundShrink = linear(Math.abs(bendRad), 0, 100, 1, 0.87);\n        var innerRad;\n        if (hoseLength > straight) {\n            innerRad = $bm_sum(straight, $bm_mul(Math.sqrt($bm_sub(Math.pow(hoseLength, 2), Math.pow(straight, 2))), roundShrink));\n            innerRad = linear(realism, 0, 100, hoseLength, innerRad);\n            innerRad = linear(Math.abs(bendDir), straight, innerRad);\n        } else {\n            innerRad = straight;\n        }\n        innerRad = linear(Math.abs(autoFlop), straight, innerRad);\n        var flopDir = 1;\n        if (bendDir < 0) {\n            flopDir = -1;\n        }\n        flopDir *= autoFlop;\n        var opp = $bm_mul($bm_sub(innerRad, straight), flopDir);\n        var theta = Math.atan($bm_div(opp, Math.max(straight, 0.001)));\n        var bendAngle = radiansToDegrees(theta);\n        if (sFac < 0) {\n            baseRot *= -1;\n        }\n        bendRad *= $bm_div($bm_div(theta, $bm_neg(Math.PI)), linear(s, hoseLength, 0, 2, 0.9));\n        var parentRot = hasParent ? parentTotal() : 0;\n        var rotCalc = $bm_sub($bm_sub($bm_sum(baseRot, bendAngle), bendRad), parentRot);\n        $bm_rt = $bm_sum(rotCalc, value);\n    } else {\n        $bm_rt = value;\n    }\n} catch (e) {\n    $bm_rt = value;\n}"}, "p": {"a": 0, "k": [662, 956.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "RubberHose 2", "np": 18, "mn": "Pseudo/3bf5uID/RubberHose_2", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Hose Length", "mn": "Pseudo/3bf5uID/RubberHose_2-0001", "ix": 1, "v": {"a": 0, "k": 240, "ix": 1}}, {"ty": 0, "nm": "Bend Radius", "mn": "Pseudo/3bf5uID/RubberHose_2-0002", "ix": 2, "v": {"a": 0, "k": 75, "ix": 2}}, {"ty": 0, "nm": "Realism", "mn": "Pseudo/3bf5uID/RubberHose_2-0003", "ix": 3, "v": {"a": 0, "k": 0, "ix": 3}}, {"ty": 0, "nm": "Bend Direction", "mn": "Pseudo/3bf5uID/RubberHose_2-0004", "ix": 4, "v": {"a": 0, "k": 0, "ix": 4}}, {"ty": 7, "nm": "Auto Rotate Start", "mn": "Pseudo/3bf5uID/RubberHose_2-0005", "ix": 5, "v": {"a": 0, "k": 1, "ix": 5}}, {"ty": 7, "nm": "Auto Rotate End", "mn": "Pseudo/3bf5uID/RubberHose_2-0006", "ix": 6, "v": {"a": 0, "k": 1, "ix": 6}}, {"ty": 6, "nm": "<PERSON>", "mn": "Pseudo/3bf5uID/RubberHose_2-0007", "ix": 7, "v": 0}, {"ty": 3, "nm": "A", "mn": "Pseudo/3bf5uID/RubberHose_2-0008", "ix": 8, "v": {"a": 0, "k": [0, 0], "ix": 8, "x": "var $bm_rt;\n$bm_rt = thisLayer.toComp([\n    0,\n    0,\n    0\n]);"}}, {"ty": 3, "nm": "B", "mn": "Pseudo/3bf5uID/RubberHose_2-0009", "ix": 9, "v": {"a": 0, "k": [0, 0], "ix": 9, "x": "var $bm_rt;\ntry {\n    var b = thisLayer(2)('Admin')(2)('B')(2)(1)._name;\n    $bm_rt = thisComp.layer(b).toComp([\n        0,\n        0,\n        0\n    ]);\n} catch (err) {\n    $bm_rt = value;\n}"}}, {"ty": 0, "nm": "Outer Radius", "mn": "Pseudo/3bf5uID/RubberHose_2-0010", "ix": 10, "v": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\nvar a = thisLayer(4)('RubberHose 2')('A');\nvar b = thisLayer(4)('RubberHose 2')('B');\nvar s = length(a, b);\n$bm_rt = $bm_mul(Math.sin(0.78539816339), s);"}}, {"ty": 0, "nm": "Inner Radius", "mn": "Pseudo/3bf5uID/RubberHose_2-0011", "ix": 11, "v": {"a": 0, "k": 0, "ix": 11, "x": "var $bm_rt;\nvar eff = thisLayer(4)('RubberHose 2');\nvar bendRad = eff('Bend Radius');\nvar hoseLength = $bm_div(eff('Hose Length'), 2);\nvar realism = eff('Realism');\nvar bendDir = $bm_div(eff('Bend Direction'), 100);\nvar sFac = eff('Parent Scale');\nvar straight = eff('Straight');\nvar autoFlop = eff('AutoFlop');\nvar roundShrink = linear(Math.abs(bendRad), 0, 100, 1, 0.87);\nvar innerRad;\nif (hoseLength > straight) {\n    innerRad = $bm_sum(straight, $bm_mul(Math.sqrt($bm_sub(Math.pow(hoseLength, 2), Math.pow(straight, 2))), roundShrink));\n    innerRad = linear(realism, 0, 100, hoseLength, innerRad);\n    innerRad = linear(Math.abs(bendDir), straight, innerRad);\n} else {\n    innerRad = straight;\n}\ninnerRad *= Math.abs(sFac);\ninnerRad = linear(Math.abs(autoFlop), $bm_mul(straight, Math.max(Math.abs(sFac), 0.001)), innerRad);\n$bm_rt = innerRad;"}}, {"ty": 0, "nm": "Straight", "mn": "Pseudo/3bf5uID/RubberHose_2-0012", "ix": 12, "v": {"a": 0, "k": 0, "ix": 12, "x": "var $bm_rt;\nvar sFac = thisLayer(4)('RubberHose 2')('Parent Scale');\nvar outerRad = $bm_div(thisLayer(4)('RubberHose 2')('Outer Radius'), Math.max(Math.abs(sFac), 0.001));\n;\n$bm_rt = $bm_div($bm_mul(1.4142135623731, outerRad), 2);"}}, {"ty": 0, "nm": "Base Rotation", "mn": "Pseudo/3bf5uID/RubberHose_2-0013", "ix": 13, "v": {"a": 0, "k": 0, "ix": 13, "x": "var $bm_rt;\nvar a = thisLayer(4)('RubberHose 2')('A');\nvar b = thisLayer(4)('RubberHose 2')('B');\n$bm_rt = radiansToDegrees(Math.atan2($bm_sub(a[1], b[1]), $bm_sub(a[0], b[0])));"}}, {"ty": 0, "nm": "AutoFlop", "mn": "Pseudo/3bf5uID/RubberHose_2-0014", "ix": 14, "v": {"a": 0, "k": 0, "ix": 14, "x": "var $bm_rt;\nvar hasAF = false, isEnabled = false, output;\ntry {\n    var lyrAF = thisComp.layer($bm_sum(thisLayer._name.split('::')[0], '::AutoFlop'));\n    isEnabled = lyrAF(4)('Enable')(1);\n    var falloffAngle = lyrAF(4)('Falloff')(1);\n    hasAF = true;\n    var a = thisLayer.toComp([\n        0,\n        0,\n        0\n    ]);\n    var b = thisComp.layer(thisLayer(2)('Admin')(2)('B')(2)(1)._name).toComp([\n        0,\n        0,\n        0\n    ]);\n} catch (e) {\n}\nif (hasAF && isEnabled == 1) {\n    var threshRot = lyrAF('ADBE Transform Group')('ADBE Rotate Z');\n    threshRot %= 360;\n    var ctrlAngle = $bm_neg(radiansToDegrees(Math.atan2($bm_sub(b[0], a[0]), $bm_sub(b[1], a[1]))));\n    var offsetAngle = $bm_sub(threshRot, ctrlAngle);\n    offsetAngle %= 360;\n    var sign = offsetAngle > 0 && offsetAngle < 180 || offsetAngle < -180 ? -1 : 1;\n    var absAngle = Math.abs(offsetAngle);\n    if (absAngle > 90) {\n        absAngle = Math.abs($bm_sub(absAngle, 180));\n    }\n    if (absAngle > 90) {\n        absAngle = Math.abs($bm_sub(absAngle, 180));\n    }\n    output = linear(absAngle, 0, falloffAngle, 0, 1);\n    output *= sign;\n} else {\n    output = 1;\n}\n$bm_rt = output;"}}, {"ty": 0, "nm": "Parent Scale", "mn": "Pseudo/3bf5uID/RubberHose_2-0015", "ix": 15, "v": {"a": 0, "k": 0, "ix": 15, "x": "var $bm_rt;\nvar sFactor = 1;\nvar scaleNorm = 0;\nvar layerChain = 'thisLayer';\nwhile (eval([layerChain][0]).hasParent) {\n    layerChain = $bm_sum(layerChain, '.parent');\n    scaleNorm = $bm_div(eval(layerChain)('ADBE Transform Group')('ADBE Scale')[0], 100);\n    sFactor = $bm_mul(sFactor, scaleNorm);\n}\n$bm_rt = sFactor;"}}, {"ty": 6, "nm": "", "mn": "Pseudo/3bf5uID/RubberHose_2-0016", "ix": 16, "v": 0}]}], "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [100, 100], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Circle", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-75, 0], [75, 0]], "c": false}, "ix": 2}, "nm": "01", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -30], [0, 30]], "c": false}, "ix": 2}, "nm": "02", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [30, 30], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "ControlShape", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.560000002384, 0, 1], "ix": 3, "x": "var $bm_rt;\nif (thisLayer.active) {\n    try {\n        var eff = thisLayer(4)('RubberHose 2');\n        var a = thisLayer.toComp([\n            0,\n            0,\n            0\n        ]);\n        var b = eff('B');\n        var straight = eff('Straight');\n        var hoseLength = $bm_div(eff('Hose Length'), 2);\n        if (straight > hoseLength) {\n            $bm_rt = [\n                0.51,\n                0.83,\n                0.98,\n                1\n            ];\n        } else {\n            $bm_rt = value;\n        }\n    } catch (err) {\n        $bm_rt = value;\n    }\n} else {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 3, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Control Point", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 4::<PERSON><PERSON>", "np": 0, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [135, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "A", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [20, 20], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 4::Shoulder", "np": 0, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "B", "np": 1, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "2.09", "np": 0, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false, "cl": "09"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Version", "np": 1, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Admin", "np": 3, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1440, "st": 0, "bm": 0}, {"ddd": 0, "ind": 28, "ty": 4, "nm": "Hose 4::Shoulder", "parent": 18, "hd": true, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\nfunction parentTotal() {\n    var parentVal = 0;\n    var layerChain = 'thisLayer';\n    while (eval([layerChain][0]).hasParent) {\n        layerChain = $bm_sum(layerChain, '.parent');\n        parentVal = $bm_sum(parentVal, eval([layerChain][0]).rotation);\n    }\n    return parentVal;\n}\ntry {\n    var eff = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    var autoRotate = eff('Auto Rotate Start');\n    if (autoRotate == 1) {\n        var a = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name).toComp([\n            0,\n            0,\n            0\n        ]);\n        var b = thisComp.layer(thisLayer(2)('Admin')(2)('B')(2)(1)._name).toComp([\n            0,\n            0,\n            0\n        ]);\n        var s = length(a, b);\n        var sFac = eff('Parent Scale');\n        var realism = eff('Realism');\n        var bendDir = $bm_div(eff('Bend Direction'), 100);\n        var hoseLength = $bm_div(eff('Hose Length'), 2);\n        var bendRad = eff('Bend Radius');\n        var autoFlop = eff('AutoFlop');\n        var baseRot = $bm_sub(180, radiansToDegrees(Math.atan2($bm_sub(b[0], a[0]), $bm_sub(b[1], a[1]))));\n        var outerRad = $bm_mul(Math.sin(0.78539816339), s);\n        var straight = $bm_div($bm_mul(1.4142135623731, outerRad), 2);\n        straight /= Math.max(Math.abs(sFac), 0.001);\n        var roundShrink = linear(Math.abs(bendRad), 0, 100, 1, 0.87);\n        var innerRad;\n        if (hoseLength > straight) {\n            innerRad = $bm_sum(straight, $bm_mul(Math.sqrt($bm_sub(Math.pow(hoseLength, 2), Math.pow(straight, 2))), roundShrink));\n            innerRad = linear(realism, 0, 100, hoseLength, innerRad);\n            innerRad = linear(Math.abs(bendDir), straight, innerRad);\n        } else {\n            innerRad = straight;\n        }\n        innerRad = linear(Math.abs(autoFlop), straight, innerRad);\n        var flopDir = 1;\n        if (bendDir < 0) {\n            flopDir = -1;\n        }\n        flopDir *= autoFlop;\n        var opp = $bm_mul($bm_sub(innerRad, straight), flopDir);\n        var theta = Math.atan($bm_div(opp, Math.max(straight, 0.001)));\n        var bendAngle = radiansToDegrees(theta);\n        if (sFac < 0) {\n            baseRot *= -1;\n        }\n        bendRad *= $bm_div($bm_div(theta, $bm_neg(Math.PI)), linear(s, hoseLength, 0, 2, 0.9));\n        var parentRot = hasParent ? parentTotal() : 0;\n        var rotCalc = $bm_sub($bm_sum($bm_sub(baseRot, bendAngle), bendRad), parentRot);\n        $bm_rt = $bm_sum(rotCalc, value);\n    } else {\n        $bm_rt = value;\n    }\n} catch (e) {\n    $bm_rt = value;\n}"}, "p": {"a": 0, "k": [96.679, 2627.639, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [100, 100], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Circle", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-75, 0], [75, 0]], "c": false}, "ix": 2}, "nm": "01", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -30], [0, 30]], "c": false}, "ix": 2}, "nm": "02", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [30, 30], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "ControlShape", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.560000002384, 0, 1], "ix": 3, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name);\n    $bm_rt = ctrl(2)('Control Point')(2)('Stroke 1')('Color');\n} catch (e) {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 3, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Control Point", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 4::<PERSON><PERSON>", "np": 0, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [135, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "A", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [20, 20], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 4::Shoulder", "np": 0, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "B", "np": 1, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "2.09", "np": 0, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false, "cl": "09"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Version", "np": 1, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Admin", "np": 3, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1440, "st": 0, "bm": 0}, {"ddd": 0, "ind": 29, "ty": 4, "nm": "Hose 4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\nfunction parentTotal() {\n    var parentVal = 0;\n    var layerChain = 'thisLayer';\n    while (eval([layerChain][0]).hasParent) {\n        layerChain = $bm_sum(layerChain, '.parent');\n        parentVal = $bm_sum(parentVal, eval([layerChain][0]).rotation);\n    }\n    return parentVal;\n}\nvar r = 0;\nif (thisLayer.hasParent) {\n    r = $bm_neg(parentTotal());\n}\n$bm_rt = r;"}, "p": {"a": 0, "k": [600, 600, 0], "ix": 2, "l": 2, "x": "var $bm_rt;\nvar p = [\n    0,\n    0\n];\ntry {\n    if (thisLayer.hasParent) {\n        p = parent.fromComp([\n            0,\n            0,\n            0\n        ]);\n    }\n    $bm_rt = p;\n} catch (err) {\n    $bm_rt = p;\n}"}, "a": {"a": 0, "k": [0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2, "x": "var $bm_rt;\n$bm_rt = $bm_div(value, length(toComp([\n    0,\n    0\n]), toComp([\n    0.7071,\n    0.7071\n]))) || 0.001;"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sr", "sy": 1, "d": 1, "pt": {"a": 0, "k": 4, "ix": 3}, "p": {"a": 0, "k": [0, 0], "ix": 4}, "r": {"a": 0, "k": 0, "ix": 5}, "ir": {"a": 0, "k": 500, "ix": 6, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "is": {"a": 0, "k": 0, "ix": 8, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "or": {"a": 0, "k": 113, "ix": 7, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "os": {"a": 0, "k": 0, "ix": 9, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "ix": 1, "nm": "LineForCurve", "mn": "ADBE Vector Shape - Star", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "e": {"a": 0, "k": 0, "ix": 2, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "o": {"a": 0, "k": -90, "ix": 3, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "m": 1, "ix": 2, "nm": "<PERSON>", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "a": {"a": 0, "k": [0, 0], "ix": 1, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "s": {"a": 0, "k": [100, 100], "ix": 3, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "r": {"a": 0, "k": 45, "ix": 6, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "o": {"a": 0, "k": 100, "ix": 7, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "sk": {"a": 0, "k": 0, "ix": 4, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "sa": {"a": 0, "k": 0, "ix": 5, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "nm": "Transform"}], "nm": "Arc", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.439215716194, 0.356862745098, 0.384313755409, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 19, "ix": 5, "x": "var $bm_rt;\nvar sFac = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2')('Parent Scale');\n$bm_rt = Math.abs($bm_mul(value, sFac));"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('A');\n} catch (err) {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "BaseHose", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Style", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 4::<PERSON><PERSON>", "np": 0, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [135, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "A", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [200, 200], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 4::Shoulder", "np": 0, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "B", "np": 1, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "2.09", "np": 0, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false, "cl": "09"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Version", "np": 1, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "sr", "sy": 1, "d": 1, "pt": {"a": 0, "k": 4, "ix": 3}, "p": {"a": 0, "k": [0, 0], "ix": 4}, "r": {"a": 0, "k": 0, "ix": 5}, "ir": {"a": 0, "k": 200, "ix": 6, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('Inner Radius');\n} catch (err) {\n    $bm_rt = value;\n}"}, "is": {"a": 0, "k": 100, "ix": 8, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('Bend Radius');\n} catch (err) {\n    $bm_rt = value;\n}"}, "or": {"a": 0, "k": 200, "ix": 7, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('Outer Radius');\n} catch (err) {\n    $bm_rt = value;\n}"}, "os": {"a": 0, "k": 0, "ix": 9}, "ix": 1, "nm": "LineForCurve", "mn": "ADBE Vector Shape - Star", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0.01, "ix": 1}, "e": {"a": 0, "k": 24.99, "ix": 2}, "o": {"a": 0, "k": -90, "ix": 3, "x": "var $bm_rt;\n$bm_rt = -90;"}, "m": 1, "ix": 2, "nm": "<PERSON>", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1, "x": "var $bm_rt;\nvar s = thisProperty.propertyGroup(2)(2)(1)(7);\n$bm_rt = [\n    -s,\n    0\n];"}, "s": {"a": 0, "k": [100, 100], "ix": 3, "x": "var $bm_rt;\nvar flop;\ntry {\n    var eff = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    var bendDir = eff('Bend Direction');\n    var autoFlop = eff('AutoFlop');\n    flop = bendDir > 0 ? 1 : -1;\n    autoFlop > 0 ? 0 : flop *= -1;\n    var s = flop == 1 ? [\n        -100,\n        100\n    ] : [\n        100,\n        100\n    ];\n    if (eff('Parent Scale') < 0) {\n        s = [\n            $bm_neg(s[0]),\n            s[1]\n        ];\n    }\n    $bm_rt = s;\n} catch (err) {\n    $bm_rt = value;\n}\n;"}, "r": {"a": 0, "k": 45, "ix": 6, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    var baseRot = ctrl('Base Rotation');\n    var flop = content('Admin').content('ArcMath').transform.scale[0];\n    var rotOffset = flop < 0 ? -45 : 225;\n    $bm_rt = $bm_sum(baseRot, rotOffset);\n} catch (err) {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "ArcMath", "np": 2, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('A');\n} catch (err) {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Admin", "np": 4, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1440, "st": 0, "bm": 0}, {"ddd": 0, "ind": 30, "ty": 4, "nm": "BG", "hd": true, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [600, 600, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [1200, 1200], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 15, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": true}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1440, "st": 0, "bm": 0}], "markers": []}