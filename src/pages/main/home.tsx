import Stack from '@mui/material/Stack';

import { BackToTop, ScrollProgress, useScrollProgress } from 'src/components/animate';

import { HomeFAQs } from 'src/sections/main/home/<USER>';
import { HomeHero } from 'src/sections/main/home/<USER>';
import { HomeMinimal } from 'src/sections/main/home/<USER>';
import { HomeTestimonials } from 'src/sections/main/home/<USER>';
import { HomeAdvertisement } from 'src/sections/main/home/<USER>';

const LandingPage = () => {
  const pageProgress = useScrollProgress();

  return (
    <>
      <ScrollProgress
        variant="linear"
        progress={pageProgress.scrollYProgress}
        sx={{ position: 'fixed' }}
      />

      <BackToTop />

      <HomeHero />

      <Stack sx={{ position: 'relative', bgcolor: 'background.default' }}>
        <HomeMinimal />

        <HomeTestimonials />

        <HomeFAQs />

        <HomeAdvertisement />
      </Stack>
    </>
  );
};

export default LandingPage;
