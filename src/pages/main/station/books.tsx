import type { IBooksTableData } from 'src/types/books';

import { useState, useEffect, useCallback } from 'react';

import { Stack } from '@mui/material';

import { useBooksData } from 'src/hooks/useSupabase/useBooks';

import { useStationContext } from 'src/station/hooks';

import BooksList from 'src/sections/main/station/books/section/books-list';
import FilterOptions from 'src/sections/main/station/books/section/filter-options';
import BooksListEmpty from 'src/sections/main/station/books/section/books-list-empty';
import BooksListLoading from 'src/sections/main/station/books/section/books-list-loading';

// ----------------------------------------------------------------------

export default function ShelfBooksView() {
  const { station } = useStationContext();
  const [isLoading, setIsLoading] = useState(true);
  const { getBooksByShelfId } = useBooksData('books');
  const [books, setBooks] = useState<IBooksTableData[]>([]);

  const { stationId } = station;

  const fetchBooks = useCallback(async () => {
    try {
      const data = await getBooksByShelfId(stationId as string);
      setBooks((data as any) || []);
    } catch (error) {
      console.error('error while fetching books data', error);
    } finally {
      setIsLoading(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [stationId]);

  useEffect(() => {
    fetchBooks();
  }, [fetchBooks]);

  return (
    <Stack gap={2}>
      <FilterOptions />
      {isLoading ? (
        <BooksListLoading />
      ) : books.length === 0 ? (
        <BooksListEmpty />
      ) : (
        <BooksList books={books} />
      )}
    </Stack>
  );
}
