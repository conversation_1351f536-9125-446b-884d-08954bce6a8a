import { z } from 'zod';
import { useState, useEffect } from 'react';

import { Box, Container, Typography, CircularProgress } from '@mui/material';

import { paths } from 'src/routes/paths';
import { useParams, useRouter } from 'src/routes/hooks';

import { useLocalStorage } from 'src/hooks/use-local-storage';

import { supabase } from 'src/lib/supabase';
import { useStationContext } from 'src/station/hooks';
import { FIRST_VISIT_STORAGE_KEY } from 'src/constants/stations';

const StationPage = () => {
  const { stationId } = useParams();
  const router = useRouter();
  const { station, setStation } = useStationContext();

  const { state: firstVisit, setState: setFirstVisit } = useLocalStorage(
    FIRST_VISIT_STORAGE_KEY,
    true
  );

  const [status, setStatus] = useState<'loading' | 'invalid' | 'not_found' | 'success'>('loading');

  const uuidSchema = z.string().uuid();

  useEffect(() => {
    const validateAndFetch = async () => {
      if (!uuidSchema.safeParse(stationId).success) {
        setStatus('invalid');
        return;
      }

      const { data, error } = await supabase
        .from('stations')
        .select('*')
        .eq('id', stationId)
        .single();

      if (error || !data) {
        setStatus('not_found');
        return;
      }

      // Set context with station info
      setStation({
        stationId: data.id,
        stationName: data.name,
        stationLocation: data.location,
        timestamp: Date.now(),
      });

      setStatus('success');
    };

    validateAndFetch();
  }, [stationId, setStation, uuidSchema]);

  useEffect(() => {
    let timeout: NodeJS.Timeout;
    if (status === 'success') {
      timeout = setTimeout(() => {
        // if the public is visiting the station for the first time, redirect to the home page. NOTE: it's direclty storing on the local storage.
        const redirectPath = firstVisit ? paths.public.root : paths.public.station.root;

        if (firstVisit) {
          // ⚠️ We are NOT using `setFirstVisit(false)` here because it updates React state,
          // which can cause a flicker or re-render just before the redirect.
          //
          // Instead, we directly write to `localStorage` to persist the "first visit" flag
          // without triggering a state update — this ensures a smooth UX with no visual glitches.
          //
          // If it's not the user's first visit, we go directly to the book list page.
          // Otherwise, we show the landing screen to guide them through how it works.
          localStorage.setItem(FIRST_VISIT_STORAGE_KEY, 'false');
        }

        router.replace(redirectPath);
      }, 1500);
    }
    return () => clearTimeout(timeout);
  }, [status, router, firstVisit, setFirstVisit, stationId]);

  return (
    <Container maxWidth="sm">
      {status === 'loading' && <LoadingLayout message="Connecting to the station..." />}

      {status === 'success' && (
        <LoadingLayout
          message={
            firstVisit
              ? `You're now connected to ${station.stationName} – ${station.stationLocation}.`
              : `Connected to ${station.stationName}.`
          }
          subMessage={
            firstVisit ? 'Let’s show you how this works...' : 'Loading available books...'
          }
        />
      )}

      {status === 'invalid' && (
        <MessageLayout
          title="Invalid station link"
          subtitle="This link looks invalid. Please scan a station QR code again."
        />
      )}

      {status === 'not_found' && (
        <MessageLayout
          title="Shelf not found"
          subtitle="This station may no longer be available. Please try scanning a different one."
        />
      )}
    </Container>
  );
};

export default StationPage;

function LoadingLayout({ message, subMessage }: { message: string; subMessage?: string }) {
  return (
    <Box textAlign="center" mt={8}>
      <CircularProgress />
      <Typography variant="body1" sx={{ mt: 2 }}>
        {message}
      </Typography>
      {subMessage && (
        <Typography variant="body2" sx={{ color: 'text.secondary', mt: 1 }}>
          {subMessage}
        </Typography>
      )}
    </Box>
  );
}

function MessageLayout({ title, subtitle }: { title: string; subtitle: string }) {
  return (
    <Box textAlign="center" mt={8}>
      <Typography variant="h5" gutterBottom>
        {title}
      </Typography>
      <Typography sx={{ color: 'text.secondary', mb: 3 }}>{subtitle}</Typography>
    </Box>
  );
}
