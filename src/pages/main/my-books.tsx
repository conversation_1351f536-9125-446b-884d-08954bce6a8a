import type { IBooksTableData } from 'src/types/books';

import { useState, useEffect, useCallback } from 'react';

import { Stack, Typography } from '@mui/material';

import { useBooksData } from 'src/hooks/useSupabase/useBooks';

import { useStationContext } from 'src/station/hooks';

import BookCard from 'src/sections/main/station/books/book-card';
import BooksListEmpty from 'src/sections/main/station/books/section/books-list-empty';
import BooksListLoading from 'src/sections/main/station/books/section/books-list-loading';

import { AuthGuard } from 'src/auth/guard/auth-guard';
import { useAuthContext } from 'src/auth/hooks/use-auth-context';

export default function MyBooksPage() {
  const { user } = useAuthContext();
  const { station, isExpired, scanStationDialogStatus } = useStationContext();
  const { getBooksByBorrowerId, loading, updateBook } = useBooksData('books');
  const [books, setBooks] = useState<IBooksTableData[]>([]);

  const fetchBooks = useCallback(async () => {
    if (user?.userId) {
      const data = await getBooksByBorrowerId(user?.userId);
      setBooks((data || []) as IBooksTableData[]);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user]);

  const handleReturnBook = async (id: string) => {
    if (isExpired()) {
      scanStationDialogStatus.onTrue();
      return;
    }

    if (!station.stationId) {
      scanStationDialogStatus.onTrue();
      return;
    }

    try {
      await updateBook(id, {
        station_id: station.stationId,
      } as any);
      fetchBooks();
    } catch (error) {
      console.error('Error returning book:', error);
    }
  };

  useEffect(() => {
    fetchBooks();
  }, [fetchBooks]);

  return (
    <AuthGuard>
      <Stack gap={2}>
        <Typography variant="h4" fontWeight="bold">
          My Borrowed Books
        </Typography>
        {loading ? (
          <BooksListLoading />
        ) : books.length === 0 ? (
          <BooksListEmpty />
        ) : (
          <Stack gap={2}>
            {books.map((book: IBooksTableData) => (
              <BookCard key={book.id} item={book} onClickReturn={() => handleReturnBook(book.id)} />
            ))}
          </Stack>
        )}
      </Stack>
    </AuthGuard>
  );
}
