import type { IBooksTableData } from 'src/types/books';

import { useState } from 'react';

import { Tab, Tabs } from '@mui/material';

import { useBoolean } from 'src/hooks/use-boolean';
import { useBooksData } from 'src/hooks/useSupabase/useBooks';

import { BOOKS_TABS } from 'src/constants/books';

import { Iconify } from 'src/components/iconify';
import { DashboardPageWrapper } from 'src/components/dashboard-page-wrapper';

import BookUpsertDialog from 'src/sections/admin/books/book-upsert-dialog';
import BooksTableSection from 'src/sections/admin/books/books-table-section';
import BookRequestsTableSection from 'src/sections/admin/books/book-requests-table-section';

export default function BooksManagementPage() {
  // -- Dialog state --
  const bookDialog = useBoolean();
  const [selectedBook, setSelectedBook] = useState<IBooksTableData | null>(null);

  // -- Tab state --
  const [currentTab, setCurrentTab] = useState<string>(BOOKS_TABS[0].name);

  const {
    booksData,
    loading,
    rejectBookRequest,
    acceptBookRequest,
    createBook,
    deleteBook,
    updateBook,
    setBooksData,
  } = useBooksData('books');

  // -- Handlers --
  const handleOpenCreateBookDialog = () => {
    setSelectedBook(null);
    bookDialog.onTrue();
  };

  const handleEditBook = (book: IBooksTableData) => {
    setSelectedBook(book);
    bookDialog.onTrue();
  };

  const handleDialogClose = () => {
    bookDialog.onFalse();
    setSelectedBook(null);
  };

  const handleCreateBook = async (bookData: Omit<IBooksTableData, 'id'>) => {
    try {
      await createBook(bookData);
      handleDialogClose();
    } catch (error) {
      console.error('Error creating book:', error);
    }
  };

  const handleUpdateBook = async (id: string, updates: Partial<IBooksTableData>) => {
    try {
      console.log('Updating book:', id, updates);
      await updateBook(id, updates);
      setBooksData((prevBooks: any) =>
        prevBooks.map((book: any) => (book.id === id ? updates : book))
      );
      handleDialogClose();
    } catch (error) {
      console.error('Error updating book:', error);
    }
  };

  const handleDeleteBook = async (id: string) => {
    try {
      console.log('Deleting book:', id);
      await deleteBook(id);
      setBooksData((prevBooks) => prevBooks.filter((book: any) => book.id !== id));
    } catch (error) {
      console.error('Error deleting book:', error);
    }
  };

  const handleUpdateBookStatus = async (id: string, status: boolean) => {
    try {
      console.log('Updating book status:', id, status);
      await updateBook(id, { is_active: status });
    } catch (error) {
      console.error('Error updating book status:', error);
    }
  };

  const handleAcceptRequest = async (id: string) => {
    try {
      await acceptBookRequest(id);
    } catch (error) {
      console.error('Error accepting request:', error);
    }
  };

  const handleRejectRequest = async (id: string) => {
    try {
      await rejectBookRequest(id);
    } catch (error) {
      console.error('Error rejecting request:', error);
    }
  };

  return (
    <DashboardPageWrapper
      pageTitle="Books | Dashboard"
      heading="Books Management"
      breadcrumbLinks={[
        { name: 'Dashboard', href: '#' },
        { name: 'Books', href: '#' },
      ]}
      actionButton={{
        label: 'New Book',
        onClick: handleOpenCreateBookDialog,
        show: currentTab === 'books',
      }}
    >
      <Tabs value={currentTab} onChange={(_, newVal) => setCurrentTab(newVal)} sx={{ mb: 3 }}>
        {BOOKS_TABS.map((tab) => (
          <Tab
            key={tab.name}
            sx={{ fontWeight: 'fontWeightBold' }}
            value={tab.name}
            iconPosition="start"
            icon={<Iconify icon={tab.iconifyIcon} width={24} />}
            label={tab.label}
          />
        ))}
      </Tabs>

      {currentTab === 'books' &&
        booksData.filter((item) => item.status === 'approved').length > 0 && (
          <BooksTableSection
            booksData={booksData.filter((item) => item.status === 'approved')}
            loading={loading}
            onEditBook={handleEditBook}
            onDeleteBook={handleDeleteBook}
            onUpdateBookStatus={handleUpdateBookStatus}
          />
        )}

      {currentTab === 'requests' &&
        booksData.filter((item) => item.status !== 'approved' && item?.donator?.id !== null)
          .length > 0 && (
          <BookRequestsTableSection
            booksData={booksData.filter(
              (item) => item.status !== 'approved' && item?.donator?.id !== null
            )}
            loading={loading}
            onAcceptRequest={handleAcceptRequest}
            onRejectRequest={handleRejectRequest}
            onDeleteRequest={handleDeleteBook}
          />
        )}

      <BookUpsertDialog
        dialogControl={bookDialog}
        book={selectedBook}
        onClose={handleDialogClose}
        onCreateBook={handleCreateBook}
        onUpdateBook={handleUpdateBook}
      />
    </DashboardPageWrapper>
  );
}
