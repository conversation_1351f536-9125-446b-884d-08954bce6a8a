import type { IStation } from 'src/types/stations';

import { useBoolean } from 'src/hooks/use-boolean';
import { useStationList } from 'src/hooks/useSupabase/useStations';

import { toast } from 'src/components/snackbar';
import { EmptyContent } from 'src/components/empty-content';
import { DashboardPageWrapper } from 'src/components/dashboard-page-wrapper';

import { StationsList } from 'src/sections/admin/stations/section/stations';
import StationNewDialog from 'src/sections/admin/stations/station-new-dialog';
import { StationsSkeletonList } from 'src/sections/admin/stations/section/station-skeleton';

export default function Page() {
  const dialog = useBoolean();

  const { data, setData, isLoading, error } = useStationList();

  // Show initial load error if any
  if (error) {
    toast.error(error);
  }

  // CREATE
  const handleCreateStation = async (insertedData: IStation) => {
    setData((prevStations) => [...prevStations, insertedData]);
  };

  // UPDATE ACTIVE STATUS
  const handleUpdateActiveStatus = async (id: string, isActive: IStation['isActive']) => {
    setData((prevStations) =>
      prevStations.map((station) => (station.id === id ? { ...station, isActive } : station))
    );
  };

  // DELETE
  const handleDelete = async (id: string) => {
    setData((prevStations) => prevStations.filter((station) => station.id !== id));
  };

  return (
    <DashboardPageWrapper
      pageTitle="Station: List"
      heading="Stations"
      breadcrumbLinks={[{ name: 'Dashboard' }, { name: 'Stations' }]}
      actionButton={{
        label: 'New Station',
        onClick: dialog.onTrue,
        show: true,
      }}
    >
      {isLoading ? (
        <StationsSkeletonList />
      ) : data.length === 0 ? (
        <EmptyContent
          title="No stations are there!"
          imgUrl="/assets/icons/empty/ic_secondary_shape.svg"
          actionButton={{
            label: 'Add One',
            onClick: dialog.onTrue,
          }}
        />
      ) : (
        <StationsList
          stations={data}
          onUpdateActiveStatus={handleUpdateActiveStatus}
          onDelete={handleDelete}
        />
      )}

      {dialog.value && <StationNewDialog dialog={dialog} onCreateStation={handleCreateStation} />}
    </DashboardPageWrapper>
  );
}
