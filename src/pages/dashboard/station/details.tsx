import type { IStationData } from 'src/types/stations';

import { z } from 'zod';
import { useState } from 'react';

import { Box, Tab, Tabs } from '@mui/material';

import { paths } from 'src/routes/paths';
import { useParams, useRouter } from 'src/routes/hooks';

import { useStationDetails } from 'src/hooks/useSupabase/useStations';

import { STATION_TABS } from 'src/constants/stations';

import { Iconify } from 'src/components/iconify';
import { DashboardPageWrapper } from 'src/components/dashboard-page-wrapper';

import ShelfEditViewGeneral from 'src/sections/admin/stations/section/stations-edit-view-general';
import ShelfEditViewGeneralError from 'src/sections/admin/stations/section/station-edit-view-general-error';
import ShelfEditViewGeneralSkeleton from 'src/sections/admin/stations/section/station-edit-view-general-skelton';

import { BookListView } from './working/book-list-view';

export default function Page() {
  const { stationId } = useParams();

  const uuidSchema = z.string().uuid();

  const navigate = useRouter();

  if (!uuidSchema.safeParse(stationId).success) {
    navigate.replace(paths.page404);
  }

  const { data, isLoading, error, setData, refetch } = useStationDetails(stationId!);

  // const { data: bookData, isLoading: isBookDataLoading } = useBooksList({ stationId });

  const [currentTab, setCurrentTab] = useState<string>(STATION_TABS[0].name);

  const handleUpdateStation = async (updates: Omit<IStationData, 'id'>) => {
    setData({ id: stationId!, ...updates });
  };

  return (
    <DashboardPageWrapper
      pageTitle="Shelf: List"
      heading="Stations"
      breadcrumbLinks={[
        { name: 'Dashboard', href: '#' },
        { href: paths.dashboard.stations.root, name: 'Stations' },
        { name: data?.name, loading: true },
      ]}
    >
      <Tabs value={currentTab} onChange={(e, newVal) => setCurrentTab(newVal)}>
        {STATION_TABS.map((type) => (
          <Tab
            key={type.name}
            sx={{ fontWeight: 'fontWeightBold' }}
            value={type.name}
            iconPosition="start"
            icon={<Iconify icon={type.iconifyIcon} width={24} />}
            label={type.name}
          />
        ))}
      </Tabs>

      <Box sx={{ mt: 3 }} />

      {currentTab === 'general' &&
        (isLoading ? (
          <ShelfEditViewGeneralSkeleton />
        ) : !error && data ? (
          <ShelfEditViewGeneral station={data} onUpdateStation={handleUpdateStation} />
        ) : (
          <ShelfEditViewGeneralError
            error={error ?? ''}
            retryButton={{
              onClick: refetch,
              label: 'Retry',
            }}
          />
        ))}

      {currentTab === 'books' && <BookListView />}

      {/* {currentTab === 'books' && <StationEditViewBooks stationId={stationId!} />} */}
      {/* {currentTab === 'books' && (
        <BookListView
          booksData={bookData}
          loading={isBookDataLoading}
          onEditBook={handleEditBook}
          onDeleteBook={handleDeleteBook}
          onUpdateBookStatus={handleUpdateBookStatus}
        />
      )} */}
    </DashboardPageWrapper>
  );
}
