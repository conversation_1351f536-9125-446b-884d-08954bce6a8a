import type { ICategoryTableData, ICategoryTableFilters } from 'src/types/categories';

import { isEqual } from 'lodash';
import { useMemo, useState } from 'react';

import Card from '@mui/material/Card';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableContainer from '@mui/material/TableContainer';

import { useBoolean } from 'src/hooks/use-boolean';
import { useDebounce } from 'src/hooks/use-debounce';
import { useBooksData } from 'src/hooks/useSupabase/useBooks';
import { useCategoryData } from 'src/hooks/useSupabase/useCategories';

import { TABLE_DEFAULTS } from 'src/constants/table';
import { CATEGORY_TABLE_COLUMNS, DEFAULT_CATEGORY_TABLE_FILTERS } from 'src/constants/categories';

import { toast } from 'src/components/snackbar';
import { Scrollbar } from 'src/components/scrollbar';
import { EmptyContent } from 'src/components/empty-content';
import { DashboardPageWrapper } from 'src/components/dashboard-page-wrapper';
import {
  useTable,
  emptyRows,
  TableNoData,
  TableEmptyRows,
  TableHeadCustom,
  TablePaginationCustom,
} from 'src/components/table';

import CategoriesTableToolbar from 'src/sections/admin/categories/premises-toolbar';
import CategoriesTableRow from 'src/sections/admin/categories/categories-table-row';
import CategoryUpsertDialog from 'src/sections/admin/categories/categories-upsert-dialog';
import CategoriesTableFiltersResult from 'src/sections/admin/categories/categories-filter-result';

export default function TestsListView() {
  // -- Dialog state --
  const categoryDialog = useBoolean();
  const [selectedCategory, setSelectedCategory] = useState<ICategoryTableData | null>(null);

  // -- Table state and config --
  const tableConfig = useTable();
  const [filters, setFilters] = useState(DEFAULT_CATEGORY_TABLE_FILTERS);

  // -- Derived values --
  const denseHeight = tableConfig.dense
    ? TABLE_DEFAULTS.DENSE_HEIGHT
    : TABLE_DEFAULTS.DENSE_HEIGHT_WITH_MARGIN;

  const debouncedsearchQuery: any = useDebounce(filters.searchKeyword, 1500);

  const {
    data,
    loading: categoryLoading,
    createCategory,
    deleteCategory,
    updateCategory,
  } = useCategoryData('categories');
  const { booksData, loading: booksLoading } = useBooksData('books');

  const bookCounts = useMemo(() => {
    const counts = new Map<string, number>();
    booksData.forEach((book) => {
      if (book.category) {
        const count = counts.get(book.category.id) || 0;
        counts.set(book.category.id, count + 1);
      }
    });
    return counts;
  }, [booksData]);
  // -- Transform data to match component expectations --
  const transformedData =
    data?.map((item) => ({
      ...item,
      total_books: bookCounts.get(item.id) || 0,
      isActive: item.is_active || false,
    })) || [];

  // -- Apply filters to the data --
  const filteredData = transformedData.filter((item) => {
    // Search filter - search in name and description (case-insensitive, trimmed)
    const searchTerm = debouncedsearchQuery.trim().toLowerCase();
    const matchesSearch = searchTerm
      ? item.name.toLowerCase().includes(searchTerm) ||
        item.description.toLowerCase().includes(searchTerm)
      : true;

    // Status filter
    // const matchesStatus =
    //   filters.isActive !== undefined ? item.isActive === filters.isActive : true;

    return matchesSearch;
  });

  // -- Derived table data --
  const tableData = filteredData;
  const totalCount = filteredData.length;
  const isFetching = categoryLoading || booksLoading;

  // -- Computed helpers --
  const canReset = !isEqual(DEFAULT_CATEGORY_TABLE_FILTERS, filters);
  const emptyData = !tableData.length;
  const hasData = transformedData.length > 0;
  const isFiltered = debouncedsearchQuery.length > 0 || filters.isActive !== undefined;
  const shouldShowTable = isFetching || hasData;

  // -- Handlers --
  const handleOpenCreateCategoryDialog = () => {
    categoryDialog.onTrue();
  };

  const handleView = (category: ICategoryTableData) => {
    // Transform the category data to match dialog expectations
    const transformedCategory = {
      ...category,

      isActive: category.is_active ?? false,
    };
    setSelectedCategory(transformedCategory);
    categoryDialog.onTrue();
  };

  const handleDialogClose = () => {
    categoryDialog.onFalse();
    setSelectedCategory(null); // Clear the selected category to trigger useEffect in the dialog, ensuring the form resets to default state
  };

  const handleFilters = <K extends keyof ICategoryTableFilters>(
    name: K,
    value: ICategoryTableFilters[K]
  ) => {
    tableConfig.onResetPage();
    setFilters((prevState) => ({
      ...prevState,
      [name]: value,
    }));
  };

  const handleResetFilters = () => {
    setFilters(DEFAULT_CATEGORY_TABLE_FILTERS);
  };

  const handleDeleteCategory = async (id: string) => {
    try {
      await deleteCategory(id);
      toast.success('Category deleted successfully!');
      console.log('Category deleted successfully');
    } catch (error) {
      console.error('Error deleting category:', error);
      toast.error('Failed to delete category. Please try again.');
    }
  };

  const handleUpdateCategoryStatus = async (id: string, status: boolean) => {
    try {
      await updateCategory(id, { is_active: status });
      toast.success(`Category ${status ? 'activated' : 'deactivated'} successfully!`);
      console.log('Category status updated successfully');
    } catch (error) {
      console.error('Error updating category status:', error);
      toast.error('Failed to update category status. Please try again.');
    }
  };

  return (
    <DashboardPageWrapper
      pageTitle="Categories | Dashboard"
      heading="Categories"
      breadcrumbLinks={[
        { name: 'Dashboard', href: '#' },
        { name: 'Categories', href: '#' },
      ]}
      actionButton={{
        label: 'New Category',
        onClick: handleOpenCreateCategoryDialog,
        show: true,
      }}
    >
      {shouldShowTable ? (
        <Card>
          <CategoriesTableToolbar filters={filters} onFilters={handleFilters} />

          {canReset && (
            <CategoriesTableFiltersResult
              filters={filters}
              onFilters={handleFilters}
              onResetFilters={handleResetFilters}
              results={totalCount}
              sx={{ p: 2.5, pt: 0 }}
            />
          )}
          {/* category design ui  */}
          <TableContainer sx={{ position: 'relative', overflow: 'unset' }}>
            <Scrollbar>
              <Table size={tableConfig.dense ? 'small' : 'medium'} sx={{ minWidth: 960 }}>
                <TableHeadCustom headLabel={CATEGORY_TABLE_COLUMNS} />

                <TableBody>
                  {tableData.length > 0 &&
                    tableData.map((row) => (
                      <CategoriesTableRow
                        key={row.id}
                        row={row}
                        onView={() => handleView(row)}
                        onDelete={handleDeleteCategory}
                        onUpdateStatus={handleUpdateCategoryStatus}
                      />
                    ))}

                  <TableEmptyRows
                    height={denseHeight}
                    emptyRows={emptyRows(
                      tableConfig.page,
                      tableConfig.rowsPerPage,
                      tableData.length
                    )}
                  />

                  <TableNoData
                    show={emptyData}
                    title={isFiltered ? 'No categories match your search' : 'No categories found'}
                    subTitle={isFiltered ? 'Try adjusting your search or filters' : undefined}
                  />
                </TableBody>
              </Table>
            </Scrollbar>
          </TableContainer>

          <TablePaginationCustom
            count={totalCount}
            page={tableConfig.page}
            rowsPerPage={tableConfig.rowsPerPage}
            onPageChange={tableConfig.onChangePage}
            onRowsPerPageChange={tableConfig.onChangeRowsPerPage}
            dense={tableConfig.dense}
            onChangeDense={tableConfig.onChangeDense}
          />
        </Card>
      ) : (
        <EmptyContent
          title={isFiltered ? 'No categories match your search' : 'No categories yet'}
          description={
            isFiltered
              ? "Try adjusting your search terms or filters to find what you're looking for."
              : 'Get started by creating your first category to organize your books.'
          }
          imgUrl="/assets/icons/empty/ic_secondary_shape.svg"
          actionButton={
            !isFiltered
              ? {
                  label: 'Add Category',
                  onClick: handleOpenCreateCategoryDialog,
                }
              : undefined
          }
        />
      )}

      <CategoryUpsertDialog
        dialogControl={categoryDialog}
        category={selectedCategory}
        onClose={handleDialogClose}
        createCategory={createCategory}
        updateCategory={updateCategory}
      />
    </DashboardPageWrapper>
  );
}
