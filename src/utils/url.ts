/**
 * Builds a URL query string by adding or updating a parameter while preserving existing ones
 * @param searchParams - Current URL search parameters
 * @param name - Parameter name to add/update
 * @param value - Parameter value
 * @returns Complete query string with updated parameter
 */
export const buildUrlWithUpdatedParams = (
  searchParams: URLSearchParams,
  name: string,
  value: string
): string => {
  const params = new URLSearchParams(searchParams.toString());
  params.set(name, value);
  return params.toString();
};
