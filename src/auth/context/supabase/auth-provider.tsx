import type { AppUser, AuthState } from 'src/auth/types';

import { useMemo, useEffect, useCallback } from 'react';

import { useBoolean } from 'src/hooks/use-boolean';
import { useSetState } from 'src/hooks/use-set-state';

import axios from 'src/utils/axios';

import { supabase } from 'src/lib/supabase';
import { UserRoles } from 'src/constants/system';

import { AuthContext } from 'src/auth/context/auth-context';

type Props = {
  children: React.ReactNode;
};

// ----------------------------------------------------------------------

// ----------------------------------------------------------------------

const DEFAULT_STATE: AuthState = {
  user: null,
  session: null,
  loading: true,
};

export function AuthProvider({ children }: Props) {
  const { state, setState } = useSetState<AuthState>(DEFAULT_STATE);

  const signInDialogStatus = useBoolean();

  const checkUserSession = useCallback(async () => {
    try {
      const {
        data: { session },
        error,
      } = await supabase.auth.getSession();

      if (error || !session) {
        setState({ ...DEFAULT_STATE, loading: false });
        if (error) console.error(error);
        delete axios.defaults.headers.common.Authorization;
        return;
      }

      const accessToken = session.access_token;
      const { role = UserRoles.PUBLIC, user_id, user_name } = session.user.app_metadata;

      const user: AppUser = {
        email: session.user.email ?? '',
        role,
        userId: user_id,
        userName: user_name,
        accessToken,
        photoURL: session.user.user_metadata?.avatar_url ?? '',
      };

      setState({ user, loading: false, session });
      axios.defaults.headers.common.Authorization = `Bearer ${accessToken}`;
    } catch (error) {
      console.error(error);
      setState({ ...DEFAULT_STATE, loading: false });
    } finally {
      signInDialogStatus.onFalse();
    }
  }, [setState, signInDialogStatus]);

  // Listen for auth state changes
  useEffect(() => {
    const initAuth = async () => {
      // Check current session immediately
      await checkUserSession();

      const { data: authListener } = supabase.auth.onAuthStateChange(async (event, session) => {
        // Skip INITIAL_SESSION since we already checked above
        if (event === 'INITIAL_SESSION') return;

        if (event === 'SIGNED_IN' && session) {
          await checkUserSession();
        } else if (event === 'SIGNED_OUT') {
          setState({ ...DEFAULT_STATE, loading: false });
          delete axios.defaults.headers.common.Authorization;
        } else if (event === 'TOKEN_REFRESHED' && session) {
          await checkUserSession();
        }
      });

      return authListener;
    };

    const cleanup = initAuth();

    return () => {
      cleanup.then((authListener) => {
        authListener.subscription.unsubscribe();
      });
    };
  }, [checkUserSession, setState]);

  const checkAuthenticated = state.user ? 'authenticated' : 'unauthenticated';

  const status = state.loading ? 'loading' : checkAuthenticated;

  const memoizedValue = useMemo(
    () => ({
      ...state,
      checkUserSession,
      authenticated: status === 'authenticated',
      unauthenticated: status === 'unauthenticated',
      signInDialogStatus,
    }),
    [checkUserSession, state, status, signInDialogStatus]
  );

  return <AuthContext.Provider value={memoizedValue}>{children}</AuthContext.Provider>;
}
