import { supabase } from 'src/lib/supabase';

export type GoogleSignInOptions = {
  redirectTo?: string;
  scopes?: string;
  queryParams?: {
    access_type?: 'online' | 'offline';
    prompt?: 'none' | 'consent' | 'select_account';
    hd?: string;
  };
};

/** **************************************
 * Sign in with Google OAuth
 *************************************** */
export const signInWithGoogle = async (options?: GoogleSignInOptions) => {
  try {
    const { error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options,
    });

    if (error) throw error;
  } catch (error) {
    console.error('Google sign-in error:', error);
    throw error;
  }
};
/** **************************************
 * Sign out
 *************************************** */
export const signOut = async (): Promise<void> => {
  try {
    const { error } = await supabase.auth.signOut();

    if (error) {
      throw error;
    }
  } catch (error) {
    console.error('Sign out error', error);
    throw error;
  }
};
