import type { UserRoles } from 'src/constants/system';

import { useState, useEffect } from 'react';

import { paths } from 'src/routes/paths';
import { useRouter, usePathname, useSearchParams } from 'src/routes/hooks';

import { buildUrlWithUpdatedParams } from 'src/utils/url';

import { AUTH_RETURN_TO_PARAM } from 'src/constants/auth';

import { SplashScreen } from 'src/components/loading-screen';

import { useAuthContext } from 'src/auth/hooks';

// ----------------------------------------------------------------------

type Props = {
  children: React.ReactNode;
  allowedRoles?: UserRoles[];
};

export function AuthGuard({ children, allowedRoles = [] }: Props) {
  const router = useRouter();

  const pathname = usePathname();

  const searchParams = useSearchParams();

  const { authenticated, loading, user } = useAuthContext();

  const [isChecking, setIsChecking] = useState<boolean>(true);

  const checkPermissions = async (): Promise<void> => {
    if (!authenticated) {
      const signInPath = paths.auth.signIn;
      const queryString = buildUrlWithUpdatedParams(searchParams, AUTH_RETURN_TO_PARAM, pathname);
      const href = `${signInPath}?${queryString}`;

      router.replace(href);
      return;
    }

    if (allowedRoles.length > 0 && (!user?.role || !allowedRoles.includes(user?.role))) {
      router.replace(paths.page403);
    }

    setIsChecking(false);
  };

  useEffect(() => {
    if (loading) return;

    checkPermissions();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [authenticated, loading]);

  if (isChecking) {
    return <SplashScreen />;
  }

  return <>{children}</>;
}
