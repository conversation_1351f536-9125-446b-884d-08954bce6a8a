import type { Session } from '@supabase/supabase-js';
import type { UserRoles } from 'src/constants/system';
import type { IUseBooleanReturn } from 'src/hooks/use-boolean';

export type AppUser = {
  email: string;
  role: UserRoles;
  userId: string;
  userName: string;
  photoURL: string;
  accessToken: string;
};

// The full auth state
export type AuthState = {
  user: AppUser | null;
  loading: boolean;
  session: Session | null;
};

export type AuthContextValue = AuthState & {
  checkUserSession?: () => Promise<void>;
  authenticated: boolean;
  unauthenticated: boolean;
  signInDialogStatus: IUseBooleanReturn;
};
